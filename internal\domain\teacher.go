package domain

import (
    "time"
)

type Teacher struct {
    ID          int       `json:"id" db:"id"`
    UserID      int       `json:"user_id" db:"user_id"`
    EmployeeID  string    `json:"employee_id" db:"employee_id"`
    Department  string    `json:"department" db:"department"`
    Subject     string    `json:"subject" db:"subject"`
    Qualification string  `json:"qualification" db:"qualification"`
    Experience  int       `json:"experience" db:"experience"`
    Phone       string    `json:"phone" db:"phone"`
    HireDate    time.Time `json:"hire_date" db:"hire_date"`
    Salary      float64   `json:"salary" db:"salary"`
    CreatedAt   time.Time `json:"created_at" db:"created_at"`
    UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
    
    // Embedded user information
    User *User `json:"user,omitempty"`
}

type CreateTeacherRequest struct {
    UserID        int     `json:"user_id"`
    EmployeeID    string  `json:"employee_id"`
    Department    string  `json:"department"`
    Subject       string  `json:"subject"`
    Qualification string  `json:"qualification"`
    Experience    int     `json:"experience"`
    Phone         string  `json:"phone"`
    Salary        float64 `json:"salary"`
}
