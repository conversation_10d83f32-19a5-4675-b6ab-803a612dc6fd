package repository

import (
    "school-management-system/internal/domain"
)

type UserRepository interface {
    Create(user *domain.User) error
    GetByID(id int) (*domain.User, error)
    GetByEmail(email string) (*domain.User, error)
    GetAll() ([]*domain.User, error)
    Update(user *domain.User) error
    Delete(id int) error
}

type StudentRepository interface {
    Create(student *domain.Student) error
    GetByID(id int) (*domain.Student, error)
    GetByUserID(userID int) (*domain.Student, error)
    GetAll() ([]*domain.Student, error)
    Update(student *domain.Student) error
    Delete(id int) error
}

type TeacherRepository interface {
    Create(teacher *domain.Teacher) error
    GetByID(id int) (*domain.Teacher, error)
    GetByUserID(userID int) (*domain.Teacher, error)
    GetAll() ([]*domain.Teacher, error)
    Update(teacher *domain.Teacher) error
    Delete(id int) error
}
