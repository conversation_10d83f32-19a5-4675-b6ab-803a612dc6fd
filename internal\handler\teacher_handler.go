package handler

import (
    "context"
    "encoding/json"
    "fmt"
    "net/http"
    "strconv"
    "time"

    "school-management-system/internal/domain"
    "school-management-system/internal/events"
    "school-management-system/internal/service"

    "github.com/go-chi/chi/v5"
    "github.com/go-chi/render"
)

type TeacherHandler struct {
    teacherService *service.TeacherService
    eventBus       *events.EventBus
}

func NewTeacherHandler(teacherService *service.TeacherService, eventBus *events.EventBus) *TeacherHandler {
    return &TeacherHandler{
        teacherService: teacherService,
        eventBus:       eventBus,
    }
}

func (h *TeacherHandler) CreateTeacher(w http.ResponseWriter, r *http.Request) {
    var req domain.CreateTeacherRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    teacher, err := h.teacherService.CreateTeacher(&req)
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    // Publish teacher hired event
    event := events.Event{
        ID:     fmt.Sprintf("teacher_hired_%d_%d", teacher.ID, time.Now().Unix()),
        Type:   events.TeacherHired,
        UserID: teacher.UserID,
        Data: map[string]interface{}{
            "teacher_id":   teacher.ID,
            "teacher_name": fmt.Sprintf("%s %s", teacher.User.FirstName, teacher.User.LastName),
            "department":   teacher.Department,
            "subject":      teacher.Subject,
        },
        Timestamp: time.Now(),
    }

    // Publish event asynchronously
    go func() {
        if err := h.eventBus.Publish(context.Background(), event); err != nil {
            // Log error but don't fail the request
            fmt.Printf("Failed to publish teacher hired event: %v\n", err)
        }
    }()

    render.JSON(w, r, teacher)
}

func (h *TeacherHandler) GetTeachers(w http.ResponseWriter, r *http.Request) {
    teachers, err := h.teacherService.GetTeachers()
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, teachers)
}

func (h *TeacherHandler) GetTeacher(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid teacher ID", http.StatusBadRequest)
        return
    }

    teacher, err := h.teacherService.GetTeacher(id)
    if err != nil {
        http.Error(w, err.Error(), http.StatusNotFound)
        return
    }

    render.JSON(w, r, teacher)
}

func (h *TeacherHandler) UpdateTeacher(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid teacher ID", http.StatusBadRequest)
        return
    }

    var teacher domain.Teacher
    if err := json.NewDecoder(r.Body).Decode(&teacher); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    teacher.ID = id
    if err := h.teacherService.UpdateTeacher(&teacher); err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, teacher)
}

func (h *TeacherHandler) DeleteTeacher(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid teacher ID", http.StatusBadRequest)
        return
    }

    if err := h.teacherService.DeleteTeacher(id); err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    w.WriteHeader(http.StatusNoContent)
}
