version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: school_postgres
    environment:
      POSTGRES_DB: school_db
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    networks:
      - school_network

  redis:
    image: redis:7-alpine
    container_name: school_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - school_network

  app:
    build: .
    container_name: school_app
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**************************************/school_db?sslmode=disable
      - JWT_SECRET=your-super-secret-jwt-key
      - REDIS_URL=redis://redis:6379
      - PORT=8080
    depends_on:
      - postgres
      - redis
    networks:
      - school_network
    volumes:
      - .:/app
    working_dir: /app

volumes:
  postgres_data:
  redis_data:

networks:
  school_network:
    driver: bridge
