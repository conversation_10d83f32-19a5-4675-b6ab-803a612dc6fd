package postgres

import (
    "database/sql"
    "fmt"
    "time"

    "school-management-system/internal/domain"
)

type userRepository struct {
    db *sql.DB
}

func NewUserRepository(db *sql.DB) *userRepository {
    return &userRepository{db: db}
}

func (r *userRepository) Create(user *domain.User) error {
    query := `
        INSERT INTO users (email, password, first_name, last_name, role, is_active, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id`
    
    now := time.Now()
    user.CreatedAt = now
    user.UpdatedAt = now
    user.IsActive = true

    err := r.db.QueryRow(query, user.Email, user.Password, user.FirstName, 
        user.LastName, user.Role, user.IsActive, user.CreatedAt, user.UpdatedAt).Scan(&user.ID)
    
    if err != nil {
        return fmt.Errorf("failed to create user: %w", err)
    }
    
    return nil
}

func (r *userRepository) GetByID(id int) (*domain.User, error) {
    query := `
        SELECT id, email, password, first_name, last_name, role, is_active, created_at, updated_at
        FROM users WHERE id = $1`
    
    user := &domain.User{}
    err := r.db.QueryRow(query, id).Scan(
        &user.ID, &user.Email, &user.Password, &user.FirstName,
        &user.LastName, &user.Role, &user.IsActive, &user.CreatedAt, &user.UpdatedAt,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get user by ID: %w", err)
    }
    
    return user, nil
}

func (r *userRepository) GetByEmail(email string) (*domain.User, error) {
    query := `
        SELECT id, email, password, first_name, last_name, role, is_active, created_at, updated_at
        FROM users WHERE email = $1`
    
    user := &domain.User{}
    err := r.db.QueryRow(query, email).Scan(
        &user.ID, &user.Email, &user.Password, &user.FirstName,
        &user.LastName, &user.Role, &user.IsActive, &user.CreatedAt, &user.UpdatedAt,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get user by email: %w", err)
    }
    
    return user, nil
}

func (r *userRepository) GetAll() ([]*domain.User, error) {
    query := `
        SELECT id, email, password, first_name, last_name, role, is_active, created_at, updated_at
        FROM users ORDER BY created_at DESC`
    
    rows, err := r.db.Query(query)
    if err != nil {
        return nil, fmt.Errorf("failed to get all users: %w", err)
    }
    defer rows.Close()

    var users []*domain.User
    for rows.Next() {
        user := &domain.User{}
        err := rows.Scan(&user.ID, &user.Email, &user.Password, &user.FirstName,
            &user.LastName, &user.Role, &user.IsActive, &user.CreatedAt, &user.UpdatedAt)
        if err != nil {
            return nil, fmt.Errorf("failed to scan user: %w", err)
        }
        users = append(users, user)
    }
    
    return users, nil
}

func (r *userRepository) Update(user *domain.User) error {
    query := `
        UPDATE users 
        SET email = $1, first_name = $2, last_name = $3, role = $4, is_active = $5, updated_at = $6
        WHERE id = $7`
    
    user.UpdatedAt = time.Now()
    
    _, err := r.db.Exec(query, user.Email, user.FirstName, user.LastName,
        user.Role, user.IsActive, user.UpdatedAt, user.ID)
    
    if err != nil {
        return fmt.Errorf("failed to update user: %w", err)
    }
    
    return nil
}

func (r *userRepository) Delete(id int) error {
    query := `DELETE FROM users WHERE id = $1`
    
    _, err := r.db.Exec(query, id)
    if err != nil {
        return fmt.Errorf("failed to delete user: %w", err)
    }
    
    return nil
}
