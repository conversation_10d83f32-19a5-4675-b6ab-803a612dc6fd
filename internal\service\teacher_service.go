package service

import (
    "school-management-system/internal/domain"
    "school-management-system/internal/repository"
)

type TeacherService struct {
    teacherRepo repository.TeacherRepository
}

func NewTeacherService(teacherRepo repository.TeacherRepository) *TeacherService {
    return &TeacherService{teacherRepo: teacherRepo}
}

func (s *TeacherService) CreateTeacher(req *domain.CreateTeacherRequest) (*domain.Teacher, error) {
    teacher := &domain.Teacher{
        UserID:        req.UserID,
        EmployeeID:    req.EmployeeID,
        Department:    req.Department,
        Subject:       req.Subject,
        Qualification: req.Qualification,
        Experience:    req.Experience,
        Phone:         req.Phone,
        Salary:        req.Salary,
    }

    if err := s.teacherRepo.Create(teacher); err != nil {
        return nil, err
    }

    // Get the teacher with user information
    return s.teacherRepo.GetByID(teacher.ID)
}

func (s *TeacherService) GetTeachers() ([]*domain.Teacher, error) {
    return s.teacherRepo.GetAll()
}

func (s *TeacherService) GetTeacher(id int) (*domain.Teacher, error) {
    return s.teacherRepo.GetByID(id)
}

func (s *TeacherService) GetTeacherByUserID(userID int) (*domain.Teacher, error) {
    return s.teacherRepo.GetByUserID(userID)
}

func (s *TeacherService) UpdateTeacher(teacher *domain.Teacher) error {
    return s.teacherRepo.Update(teacher)
}

func (s *TeacherService) DeleteTeacher(id int) error {
    return s.teacherRepo.Delete(id)
}
