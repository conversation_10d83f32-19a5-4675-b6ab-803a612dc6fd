package domain

import (
    "time"
)

type UserRole string

const (
    RoleAdmin   UserRole = "admin"
    RoleTeacher UserRole = "teacher"
    RoleStudent UserRole = "student"
)

type User struct {
    ID        int       `json:"id" db:"id"`
    Email     string    `json:"email" db:"email"`
    Password  string    `json:"-" db:"password"`
    FirstName string    `json:"first_name" db:"first_name"`
    LastName  string    `json:"last_name" db:"last_name"`
    Role      UserRole  `json:"role" db:"role"`
    IsActive  bool      `json:"is_active" db:"is_active"`
    CreatedAt time.Time `json:"created_at" db:"created_at"`
    UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

type LoginRequest struct {
    Email    string `json:"email"`
    Password string `json:"password"`
}

type RegisterRequest struct {
    Email     string   `json:"email"`
    Password  string   `json:"password"`
    FirstName string   `json:"first_name"`
    LastName  string   `json:"last_name"`
    Role      UserRole `json:"role"`
}

type LoginResponse struct {
    Token string `json:"token"`
    User  User   `json:"user"`
}
