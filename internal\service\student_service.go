package service

import (
    "school-management-system/internal/domain"
    "school-management-system/internal/repository"
)

type StudentService struct {
    studentRepo repository.StudentRepository
}

func NewStudentService(studentRepo repository.StudentRepository) *StudentService {
    return &StudentService{studentRepo: studentRepo}
}

func (s *StudentService) CreateStudent(req *domain.CreateStudentRequest) (*domain.Student, error) {
    student := &domain.Student{
        UserID:      req.UserID,
        StudentID:   req.StudentID,
        Grade:       req.Grade,
        Class:       req.Class,
        DateOfBirth: req.DateOfBirth,
        Address:     req.Address,
        ParentName:  req.ParentName,
        ParentPhone: req.Pa<PERSON>,
        ParentEmail: req.ParentEmail,
    }

    if err := s.studentRepo.Create(student); err != nil {
        return nil, err
    }

    // Get the student with user information
    return s.studentRepo.GetByID(student.ID)
}

func (s *StudentService) GetStudents() ([]*domain.Student, error) {
    return s.studentRepo.GetAll()
}

func (s *StudentService) GetStudent(id int) (*domain.Student, error) {
    return s.studentRepo.GetByID(id)
}

func (s *StudentService) GetStudentByUserID(userID int) (*domain.Student, error) {
    return s.studentRepo.GetByUserID(userID)
}

func (s *StudentService) UpdateStudent(student *domain.Student) error {
    return s.studentRepo.Update(student)
}

func (s *StudentService) DeleteStudent(id int) error {
    return s.studentRepo.Delete(id)
}
