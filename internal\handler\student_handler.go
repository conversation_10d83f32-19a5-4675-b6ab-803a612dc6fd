package handler

import (
    "context"
    "encoding/json"
    "fmt"
    "net/http"
    "strconv"
    "time"

    "school-management-system/internal/domain"
    "school-management-system/internal/events"
    "school-management-system/internal/service"

    "github.com/go-chi/chi/v5"
    "github.com/go-chi/render"
)

type StudentHandler struct {
    studentService *service.StudentService
    eventBus       *events.EventBus
}

func NewStudentHandler(studentService *service.StudentService, eventBus *events.EventBus) *StudentHandler {
    return &StudentHandler{
        studentService: studentService,
        eventBus:       eventBus,
    }
}

func (h *StudentHandler) CreateStudent(w http.ResponseWriter, r *http.Request) {
    var req domain.CreateStudentRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    student, err := h.studentService.CreateStudent(&req)
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    // Publish student enrollment event
    event := events.Event{
        ID:     fmt.Sprintf("student_enrolled_%d_%d", student.ID, time.Now().Unix()),
        Type:   events.StudentEnrolled,
        UserID: student.UserID,
        Data: map[string]interface{}{
            "student_id":   student.ID,
            "student_name": fmt.Sprintf("%s %s", student.User.FirstName, student.User.LastName),
            "grade":        student.Grade,
            "class":        student.Class,
        },
        Timestamp: time.Now(),
    }

    // Publish event asynchronously
    go func() {
        if err := h.eventBus.Publish(context.Background(), event); err != nil {
            // Log error but don't fail the request
            fmt.Printf("Failed to publish student enrollment event: %v\n", err)
        }
    }()

    render.JSON(w, r, student)
}

func (h *StudentHandler) GetStudents(w http.ResponseWriter, r *http.Request) {
    students, err := h.studentService.GetStudents()
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, students)
}

func (h *StudentHandler) GetStudent(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid student ID", http.StatusBadRequest)
        return
    }

    student, err := h.studentService.GetStudent(id)
    if err != nil {
        http.Error(w, err.Error(), http.StatusNotFound)
        return
    }

    render.JSON(w, r, student)
}

func (h *StudentHandler) UpdateStudent(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid student ID", http.StatusBadRequest)
        return
    }

    var student domain.Student
    if err := json.NewDecoder(r.Body).Decode(&student); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    student.ID = id
    if err := h.studentService.UpdateStudent(&student); err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, student)
}

func (h *StudentHandler) DeleteStudent(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid student ID", http.StatusBadRequest)
        return
    }

    if err := h.studentService.DeleteStudent(id); err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    w.WriteHeader(http.StatusNoContent)
}
