# Database Configuration
DATABASE_URL=postgres://user:password@localhost:5432/school_db?sslmode=disable

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Server Configuration
PORT=8080

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379

# Environment
ENVIRONMENT=development
