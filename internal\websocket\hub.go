package websocket

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "sync"
    "time"

    "school-management-system/internal/events"

    "github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
    ReadBufferSize:  1024,
    WriteBufferSize: 1024,
    CheckOrigin: func(r *http.Request) bool {
        return true // Allow all origins in development
    },
}

type Client struct {
    ID     string
    UserID int
    Role   string
    Hub    *Hub
    Conn   *websocket.Conn
    Send   chan []byte
}

type Hub struct {
    clients    map[*Client]bool
    broadcast  chan []byte
    register   chan *Client
    unregister chan *Client
    mu         sync.RWMutex
    eventBus   *events.EventBus
}

func NewHub(eventBus *events.EventBus) *Hub {
    hub := &Hub{
        clients:    make(map[*Client]bool),
        broadcast:  make(chan []byte, 256),
        register:   make(chan *Client),
        unregister: make(chan *Client),
        eventBus:   eventBus,
    }
    
    // Subscribe to events for real-time notifications
    eventBus.Subscribe(events.StudentEnrolled, hub.handleStudentEvent)
    eventBus.Subscribe(events.StudentUpdated, hub.handleStudentEvent)
    eventBus.Subscribe(events.TeacherHired, hub.handleTeacherEvent)
    eventBus.Subscribe(events.GradeSubmitted, hub.handleGradeEvent)
    
    go hub.run()
    return hub
}

func (h *Hub) run() {
    for {
        select {
        case client := <-h.register:
            h.mu.Lock()
            h.clients[client] = true
            h.mu.Unlock()
            log.Printf("Client %s connected (User: %d, Role: %s)", client.ID, client.UserID, client.Role)
            
        case client := <-h.unregister:
            h.mu.Lock()
            if _, ok := h.clients[client]; ok {
                delete(h.clients, client)
                close(client.Send)
            }
            h.mu.Unlock()
            log.Printf("Client %s disconnected", client.ID)
            
        case message := <-h.broadcast:
            h.mu.RLock()
            for client := range h.clients {
                select {
                case client.Send <- message:
                default:
                    delete(h.clients, client)
                    close(client.Send)
                }
            }
            h.mu.RUnlock()
        }
    }
}

// Event handlers for real-time notifications
func (h *Hub) handleStudentEvent(ctx context.Context, event events.Event) error {
    message := map[string]interface{}{
        "type":      "student_notification",
        "event":     event.Type,
        "data":      event.Data,
        "timestamp": event.Timestamp,
    }
    
    return h.broadcastToRole(message, "admin", "teacher")
}

func (h *Hub) handleTeacherEvent(ctx context.Context, event events.Event) error {
    message := map[string]interface{}{
        "type":      "teacher_notification",
        "event":     event.Type,
        "data":      event.Data,
        "timestamp": event.Timestamp,
    }
    
    return h.broadcastToRole(message, "admin")
}

func (h *Hub) handleGradeEvent(ctx context.Context, event events.Event) error {
    message := map[string]interface{}{
        "type":      "grade_notification",
        "event":     event.Type,
        "data":      event.Data,
        "timestamp": event.Timestamp,
    }
    
    // Notify the specific student and admins/teachers
    if studentID, ok := event.Data["student_id"].(int); ok {
        return h.broadcastToUserAndRoles(message, studentID, "admin", "teacher")
    }
    
    return h.broadcastToRole(message, "admin", "teacher")
}

func (h *Hub) broadcastToRole(message map[string]interface{}, roles ...string) error {
    data, err := json.Marshal(message)
    if err != nil {
        return err
    }
    
    h.mu.RLock()
    defer h.mu.RUnlock()
    
    roleMap := make(map[string]bool)
    for _, role := range roles {
        roleMap[role] = true
    }
    
    for client := range h.clients {
        if roleMap[client.Role] {
            select {
            case client.Send <- data:
            default:
                delete(h.clients, client)
                close(client.Send)
            }
        }
    }
    
    return nil
}

func (h *Hub) broadcastToUserAndRoles(message map[string]interface{}, userID int, roles ...string) error {
    data, err := json.Marshal(message)
    if err != nil {
        return err
    }
    
    h.mu.RLock()
    defer h.mu.RUnlock()
    
    roleMap := make(map[string]bool)
    for _, role := range roles {
        roleMap[role] = true
    }
    
    for client := range h.clients {
        if client.UserID == userID || roleMap[client.Role] {
            select {
            case client.Send <- data:
            default:
                delete(h.clients, client)
                close(client.Send)
            }
        }
    }
    
    return nil
}

// WebSocket handler
func (h *Hub) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
    conn, err := upgrader.Upgrade(w, r, nil)
    if err != nil {
        log.Printf("WebSocket upgrade error: %v", err)
        return
    }
    
    // Get user info from JWT token (you'd implement this)
    userID, role := h.getUserFromRequest(r)
    
    client := &Client{
        ID:     generateClientID(),
        UserID: userID,
        Role:   role,
        Hub:    h,
        Conn:   conn,
        Send:   make(chan []byte, 256),
    }
    
    client.Hub.register <- client
    
    // Start goroutines for reading and writing
    go client.writePump()
    go client.readPump()
}

func (c *Client) readPump() {
    defer func() {
        c.Hub.unregister <- c
        c.Conn.Close()
    }()
    
    c.Conn.SetReadLimit(512)
    c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
    c.Conn.SetPongHandler(func(string) error {
        c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
        return nil
    })
    
    for {
        _, _, err := c.Conn.ReadMessage()
        if err != nil {
            break
        }
    }
}

func (c *Client) writePump() {
    ticker := time.NewTicker(54 * time.Second)
    defer func() {
        ticker.Stop()
        c.Conn.Close()
    }()
    
    for {
        select {
        case message, ok := <-c.Send:
            c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
            if !ok {
                c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
                return
            }
            
            if err := c.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
                return
            }
            
        case <-ticker.C:
            c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
            if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
                return
            }
        }
    }
}

func (h *Hub) getUserFromRequest(r *http.Request) (int, string) {
    // Extract user info from JWT token or session
    // This is a placeholder - implement based on your auth system
    return 1, "admin"
}

func generateClientID() string {
    return fmt.Sprintf("client_%d", time.Now().UnixNano())
}
