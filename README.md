# School Management System

A comprehensive school management system built with Go, featuring user management, student enrollment, teacher management, real-time notifications, and more.

## Features

- **User Management**: Admin, Teacher, and Student roles with JWT authentication
- **Student Management**: Student enrollment, profile management, and academic tracking
- **Teacher Management**: Teacher profiles, department assignments, and subject management
- **Real-time Notifications**: WebSocket-based notifications for system events
- **Event-Driven Architecture**: Asynchronous event processing for system actions
- **Background Jobs**: Automated tasks for attendance reminders and grade calculations
- **RESTful API**: Clean REST API design with proper HTTP status codes
- **Database Integration**: PostgreSQL with proper migrations
- **Docker Support**: Containerized deployment with Docker Compose

## Tech Stack

- **Backend**: Go 1.21+
- **Database**: PostgreSQL 15+
- **Cache**: Redis 7+
- **Authentication**: JWT tokens
- **WebSockets**: Gorilla WebSocket
- **HTTP Router**: Chi v5
- **Password Hashing**: bcrypt
- **Environment**: Docker & Docker Compose

## Project Structure

```
school-management-system/
├── cmd/
│   └── server/
│       └── main.go                 # Application entry point
├── internal/
│   ├── api/                        # HTTP routes and handlers
│   │   ├── router.go
│   │   ├── user_routes.go
│   │   ├── student_routes.go
│   │   ├── teacher_routes.go
│   │   └── websocket_routes.go
│   ├── config/                     # Configuration management
│   │   └── config.go
│   ├── domain/                     # Domain models and DTOs
│   │   ├── user.go
│   │   ├── student.go
│   │   └── teacher.go
│   ├── repository/                 # Data access layer
│   │   ├── interfaces.go
│   │   └── postgres/
│   │       ├── user_repository.go
│   │       ├── student_repository.go
│   │       └── teacher_repository.go
│   ├── service/                    # Business logic layer
│   │   ├── auth_service.go
│   │   ├── user_service.go
│   │   ├── student_service.go
│   │   └── teacher_service.go
│   ├── handler/                    # HTTP request handlers
│   │   ├── auth_handler.go
│   │   ├── user_handler.go
│   │   ├── student_handler.go
│   │   └── teacher_handler.go
│   ├── middleware/                 # HTTP middleware
│   │   └── auth_middleware.go
│   ├── events/                     # Event system
│   │   └── event_bus.go
│   ├── websocket/                  # WebSocket hub
│   │   └── hub.go
│   ├── background/                 # Background jobs
│   │   └── jobs.go
│   └── database/                   # Database connection
│       └── postgres.go
├── migrations/                     # Database migrations
│   └── 001_initial_schema.sql
├── docker-compose.yml              # Docker services
├── Dockerfile                      # Application container
├── Makefile                        # Build and development commands
├── go.mod                          # Go module definition
├── go.sum                          # Go module checksums
├── .env.example                    # Environment variables template
└── README.md                       # This file
```

## Getting Started

### Prerequisites

- Go 1.21 or higher
- PostgreSQL 15 or higher
- Redis 7 or higher (optional, for caching)
- Docker and Docker Compose (for containerized setup)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd school-management-system
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Using Docker Compose (Recommended)**
   ```bash
   # Start all services
   make docker-compose-up
   
   # View logs
   make docker-compose-logs
   
   # Stop services
   make docker-compose-down
   ```

4. **Manual Setup**
   ```bash
   # Install dependencies
   make deps
   
   # Set up database
   createdb school_db
   make db-migrate
   
   # Run the application
   make run
   ```

### Development

```bash
# Install development tools
make install-air
make install-lint

# Run in development mode with hot reload
make dev

# Run tests
make test

# Run all checks (format, vet, lint, test)
make check
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration

### Users (Protected)
- `GET /api/v1/users` - Get all users
- `GET /api/v1/users/{id}` - Get user by ID
- `PUT /api/v1/users/{id}` - Update user
- `DELETE /api/v1/users/{id}` - Delete user

### Students (Protected)
- `GET /api/v1/students` - Get all students
- `POST /api/v1/students` - Create student
- `GET /api/v1/students/{id}` - Get student by ID
- `PUT /api/v1/students/{id}` - Update student
- `DELETE /api/v1/students/{id}` - Delete student

### Teachers (Protected)
- `GET /api/v1/teachers` - Get all teachers
- `POST /api/v1/teachers` - Create teacher
- `GET /api/v1/teachers/{id}` - Get teacher by ID
- `PUT /api/v1/teachers/{id}` - Update teacher
- `DELETE /api/v1/teachers/{id}` - Delete teacher

### WebSocket
- `GET /api/v1/ws` - WebSocket connection for real-time notifications

## Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

Default admin credentials:
- Email: `<EMAIL>`
- Password: `admin123`

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
