package api

import (
	"database/sql"
	"net/http"

	"school-management-system/internal/background"
	"school-management-system/internal/config"
	"school-management-system/internal/events"
	"school-management-system/internal/handler"
	"school-management-system/internal/middleware"
	"school-management-system/internal/repository/postgres"
	"school-management-system/internal/service"
	"school-management-system/internal/websocket"

	"github.com/go-chi/chi/v5"
	chiMiddleware "github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
)

type Router struct {
	db  *sql.DB
	cfg *config.Config
}

func NewRouter(db *sql.DB, cfg *config.Config) *chi.Mux {
	router := &Router{
		db:  db,
		cfg: cfg,
	}

	return router.setupRoutes()
}

func (router *Router) setupRoutes() *chi.Mux {
	r := chi.NewRouter()

	// Global middleware
	r.Use(chiMiddleware.Logger)
	r.Use(chiMiddleware.Recoverer)
	r.Use(cors.Handler(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token"},
		ExposedHeaders:   []string{"Link"},
		AllowCredentials: true,
		MaxAge:           300,
	}))

	// Initialize dependencies
	deps := router.initializeDependencies()

	// Start background services
	deps.JobManager.Start()

	// Setup API routes
	r.Route("/api/v1", func(r chi.Router) {
		// Public routes
		router.setupPublicRoutes(r, deps)

		// WebSocket routes (can be public or protected based on your needs)
		router.setupWebSocketRoutes(r, deps)

		// Protected routes
		router.setupProtectedRoutes(r, deps)
	})

	return r
}

type Dependencies struct {
	// Core Services
	AuthService    *service.AuthService
	UserService    *service.UserService
	StudentService *service.StudentService
	TeacherService *service.TeacherService

	// Handlers
	AuthHandler    *handler.AuthHandler
	UserHandler    *handler.UserHandler
	StudentHandler *handler.StudentHandler
	TeacherHandler *handler.TeacherHandler

	// Middleware
	AuthMiddleware *middleware.AuthMiddleware

	// Event System
	EventBus *events.EventBus

	// WebSocket
	WebSocketHub *websocket.Hub

	// Background Jobs
	JobManager *background.JobManager
}

func (router *Router) initializeDependencies() *Dependencies {
	// Initialize repositories
	userRepo := postgres.NewUserRepository(router.db)
	studentRepo := postgres.NewStudentRepository(router.db)
	teacherRepo := postgres.NewTeacherRepository(router.db)

	// Initialize services
	authService := service.NewAuthService(userRepo, router.cfg.JWTSecret)
	userService := service.NewUserService(userRepo)
	studentService := service.NewStudentService(studentRepo)
	teacherService := service.NewTeacherService(teacherRepo)

	// Initialize event system
	eventBus := events.NewEventBus(1000, 5) // 1000 buffer, 5 workers

	// Initialize WebSocket hub
	webSocketHub := websocket.NewHub(eventBus)

	// Initialize background job manager
	jobManager := background.NewJobManager(eventBus, studentService, teacherService)

	// Initialize handlers
	authHandler := handler.NewAuthHandler(authService)
	userHandler := handler.NewUserHandler(userService)
	studentHandler := handler.NewStudentHandler(studentService, eventBus) // Pass eventBus for publishing events
	teacherHandler := handler.NewTeacherHandler(teacherService, eventBus)

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(router.cfg.JWTSecret)

	return &Dependencies{
		AuthService:    authService,
		UserService:    userService,
		StudentService: studentService,
		TeacherService: teacherService,
		AuthHandler:    authHandler,
		UserHandler:    userHandler,
		StudentHandler: studentHandler,
		TeacherHandler: teacherHandler,
		AuthMiddleware: authMiddleware,
		EventBus:       eventBus,
		WebSocketHub:   webSocketHub,
		JobManager:     jobManager,
	}
}

func (router *Router) setupPublicRoutes(r chi.Router, deps *Dependencies) {
	// Authentication routes
	r.Post("/auth/login", deps.AuthHandler.Login)
	r.Post("/auth/register", deps.AuthHandler.Register)

	// Health check
	r.Get("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(200)
		w.Write([]byte("OK"))
	})
}

func (router *Router) setupProtectedRoutes(r chi.Router, deps *Dependencies) {
	r.Group(func(r chi.Router) {
		r.Use(deps.AuthMiddleware.Authenticate)

		// User routes
		router.setupUserRoutes(r, deps)

		// Student routes
		router.setupStudentRoutes(r, deps)

		// Teacher routes
		router.setupTeacherRoutes(r, deps)
	})
}
