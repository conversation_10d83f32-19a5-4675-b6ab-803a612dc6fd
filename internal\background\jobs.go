package background

import (
    "context"
    "fmt"
    "log"
    "sync"
    "time"

    "school-management-system/internal/events"
    "school-management-system/internal/service"
)

type JobManager struct {
    eventBus       *events.EventBus
    studentService *service.StudentService
    teacherService *service.TeacherService
    ctx            context.Context
    cancel         context.CancelFunc
    wg             sync.WaitGroup
}

func NewJobManager(eventBus *events.EventBus, studentService *service.StudentService, teacherService *service.TeacherService) *JobManager {
    ctx, cancel := context.WithCancel(context.Background())
    
    return &JobManager{
        eventBus:       eventBus,
        studentService: studentService,
        teacherService: teacherService,
        ctx:            ctx,
        cancel:         cancel,
    }
}

func (jm *JobManager) Start() {
    // Start daily cleanup job
    jm.wg.Add(1)
    go jm.dailyCleanupJob()
    
    // Start attendance reminder job
    jm.wg.Add(1)
    go jm.attendanceReminderJob()
    
    // Start grade calculation job
    jm.wg.Add(1)
    go jm.gradeCalculationJob()
    
    log.Println("Background job manager started")
}

func (jm *JobManager) Stop() {
    jm.cancel()
    jm.wg.Wait()
    log.Println("Background job manager stopped")
}

func (jm *JobManager) dailyCleanupJob() {
    defer jm.wg.Done()
    
    ticker := time.NewTicker(24 * time.Hour)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            log.Println("Running daily cleanup job...")
            if err := jm.performDailyCleanup(); err != nil {
                log.Printf("Daily cleanup job failed: %v", err)
            }
        case <-jm.ctx.Done():
            return
        }
    }
}

func (jm *JobManager) attendanceReminderJob() {
    defer jm.wg.Done()
    
    // Run every hour during school hours (8 AM - 6 PM)
    ticker := time.NewTicker(1 * time.Hour)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            now := time.Now()
            if now.Hour() >= 8 && now.Hour() <= 18 {
                log.Println("Checking attendance reminders...")
                if err := jm.sendAttendanceReminders(); err != nil {
                    log.Printf("Attendance reminder job failed: %v", err)
                }
            }
        case <-jm.ctx.Done():
            return
        }
    }
}

func (jm *JobManager) gradeCalculationJob() {
    defer jm.wg.Done()
    
    // Run at midnight every day
    ticker := time.NewTicker(1 * time.Hour)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            now := time.Now()
            if now.Hour() == 0 { // Midnight
                log.Println("Running grade calculations...")
                if err := jm.calculateGrades(); err != nil {
                    log.Printf("Grade calculation job failed: %v", err)
                }
            }
        case <-jm.ctx.Done():
            return
        }
    }
}

func (jm *JobManager) performDailyCleanup() error {
    // Clean up old sessions, temporary files, etc.
    log.Println("Performing daily cleanup tasks")
    return nil
}

func (jm *JobManager) sendAttendanceReminders() error {
    // Send reminders to teachers about attendance
    event := events.Event{
        ID:        generateEventID(),
        Type:      "attendance.reminder",
        Data:      map[string]interface{}{"message": "Please mark attendance"},
        Timestamp: time.Now(),
    }
    
    return jm.eventBus.Publish(context.Background(), event)
}

func (jm *JobManager) calculateGrades() error {
    // Calculate and update student grades
    log.Println("Calculating student grades")
    return nil
}

func generateEventID() string {
    return fmt.Sprintf("event_%d", time.Now().UnixNano())
}
