package api

import (
    "github.com/go-chi/chi/v5"
)

func (router *Router) setupStudentRoutes(r chi.Router, deps *Dependencies) {
    r.Route("/students", func(r chi.Router) {
        r.Get("/", deps.StudentHandler.GetStudents)
        r.Post("/", deps.StudentHandler.CreateStudent)
        r.Get("/{id}", deps.StudentHandler.GetStudent)
        r.Put("/{id}", deps.StudentHandler.UpdateStudent)
        r.Delete("/{id}", deps.StudentHandler.DeleteStudent)
        
        // Additional student-specific routes can be added here
        // r.Get("/{id}/grades", deps.StudentHandler.GetStudentGrades)
        // r.Get("/{id}/attendance", deps.StudentHandler.GetStudentAttendance)
    })
}
