package service

import (
    "school-management-system/internal/domain"
    "school-management-system/internal/repository"
)

type UserService struct {
    userRepo repository.UserRepository
}

func NewUserService(userRepo repository.UserRepository) *UserService {
    return &UserService{userRepo: userRepo}
}

func (s *UserService) GetUsers() ([]*domain.User, error) {
    return s.userRepo.GetAll()
}

func (s *UserService) GetUser(id int) (*domain.User, error) {
    return s.userRepo.GetByID(id)
}

func (s *UserService) UpdateUser(user *domain.User) error {
    return s.userRepo.Update(user)
}

func (s *UserService) DeleteUser(id int) error {
    return s.userRepo.Delete(id)
}
