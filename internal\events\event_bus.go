package events

import (
    "context"
    "fmt"
    "log"
    "sync"
    "time"
)

type EventType string

const (
    StudentEnrolled    EventType = "student.enrolled"
    StudentUpdated     EventType = "student.updated"
    TeacherHired       EventType = "teacher.hired"
    UserLoggedIn       EventType = "user.logged_in"
    GradeSubmitted     EventType = "grade.submitted"
    AttendanceMarked   EventType = "attendance.marked"
)

type Event struct {
    ID        string                 `json:"id"`
    Type      EventType              `json:"type"`
    Data      map[string]interface{} `json:"data"`
    UserID    int                    `json:"user_id"`
    Timestamp time.Time              `json:"timestamp"`
}

type Handler func(ctx context.Context, event Event) error

type EventBus struct {
    handlers map[EventType][]Handler
    mu       sync.RWMutex
    buffer   chan Event
    workers  int
    ctx      context.Context
    cancel   context.CancelFunc
    wg       sync.WaitGroup
}

func NewEventBus(bufferSize, workers int) *EventBus {
    ctx, cancel := context.WithCancel(context.Background())
    
    bus := &EventBus{
        handlers: make(map[EventType][]Handler),
        buffer:   make(chan Event, bufferSize),
        workers:  workers,
        ctx:      ctx,
        cancel:   cancel,
    }
    
    // Start worker goroutines
    for i := 0; i < workers; i++ {
        bus.wg.Add(1)
        go bus.worker(i)
    }
    
    return bus
}

func (bus *EventBus) Subscribe(eventType EventType, handler Handler) {
    bus.mu.Lock()
    defer bus.mu.Unlock()
    
    bus.handlers[eventType] = append(bus.handlers[eventType], handler)
    log.Printf("Handler subscribed to event type: %s", eventType)
}

func (bus *EventBus) Publish(ctx context.Context, event Event) error {
    select {
    case bus.buffer <- event:
        return nil
    case <-ctx.Done():
        return ctx.Err()
    case <-time.After(5 * time.Second):
        return fmt.Errorf("failed to publish event: buffer full")
    }
}

func (bus *EventBus) worker(id int) {
    defer bus.wg.Done()
    log.Printf("Event worker %d started", id)
    
    for {
        select {
        case event := <-bus.buffer:
            bus.handleEvent(event)
        case <-bus.ctx.Done():
            log.Printf("Event worker %d shutting down", id)
            return
        }
    }
}

func (bus *EventBus) handleEvent(event Event) {
    bus.mu.RLock()
    handlers := bus.handlers[event.Type]
    bus.mu.RUnlock()
    
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    for _, handler := range handlers {
        go func(h Handler) {
            if err := h(ctx, event); err != nil {
                log.Printf("Error handling event %s: %v", event.Type, err)
            }
        }(handler)
    }
}

func (bus *EventBus) Close() {
    bus.cancel()
    bus.wg.Wait()
    close(bus.buffer)
    log.Println("EventBus closed")
}
