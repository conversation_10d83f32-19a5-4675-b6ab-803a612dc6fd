package postgres

import (
    "database/sql"
    "fmt"
    "time"

    "school-management-system/internal/domain"
)

type teacherRepository struct {
    db *sql.DB
}

func NewTeacherRepository(db *sql.DB) *teacherRepository {
    return &teacherRepository{db: db}
}

func (r *teacherRepository) Create(teacher *domain.Teacher) error {
    query := `
        INSERT INTO teachers (user_id, employee_id, department, subject, qualification,
                             experience, phone, hire_date, salary, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING id`
    
    now := time.Now()
    teacher.CreatedAt = now
    teacher.UpdatedAt = now
    teacher.HireDate = now

    err := r.db.QueryRow(query, teacher.UserID, teacher.EmployeeID,
        teacher.Department, teacher.Subject, teacher.Qualification,
        teacher.Experience, teacher.Phone, teacher.HireDate,
        teacher.Sal<PERSON>, teacher.Created<PERSON>t, teacher.UpdatedAt).Scan(&teacher.ID)
    
    if err != nil {
        return fmt.Errorf("failed to create teacher: %w", err)
    }
    
    return nil
}

func (r *teacherRepository) GetByID(id int) (*domain.Teacher, error) {
    query := `
        SELECT t.id, t.user_id, t.employee_id, t.department, t.subject,
               t.qualification, t.experience, t.phone, t.hire_date,
               t.salary, t.created_at, t.updated_at,
               u.email, u.first_name, u.last_name, u.role, u.is_active
        FROM teachers t
        JOIN users u ON t.user_id = u.id
        WHERE t.id = $1`
    
    teacher := &domain.Teacher{User: &domain.User{}}
    err := r.db.QueryRow(query, id).Scan(
        &teacher.ID, &teacher.UserID, &teacher.EmployeeID, &teacher.Department,
        &teacher.Subject, &teacher.Qualification, &teacher.Experience,
        &teacher.Phone, &teacher.HireDate, &teacher.Salary,
        &teacher.CreatedAt, &teacher.UpdatedAt,
        &teacher.User.Email, &teacher.User.FirstName, &teacher.User.LastName,
        &teacher.User.Role, &teacher.User.IsActive,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get teacher by ID: %w", err)
    }
    
    teacher.User.ID = teacher.UserID
    return teacher, nil
}

func (r *teacherRepository) GetByUserID(userID int) (*domain.Teacher, error) {
    query := `
        SELECT id, user_id, employee_id, department, subject, qualification,
               experience, phone, hire_date, salary, created_at, updated_at
        FROM teachers WHERE user_id = $1`
    
    teacher := &domain.Teacher{}
    err := r.db.QueryRow(query, userID).Scan(
        &teacher.ID, &teacher.UserID, &teacher.EmployeeID, &teacher.Department,
        &teacher.Subject, &teacher.Qualification, &teacher.Experience,
        &teacher.Phone, &teacher.HireDate, &teacher.Salary,
        &teacher.CreatedAt, &teacher.UpdatedAt,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get teacher by user ID: %w", err)
    }
    
    return teacher, nil
}

func (r *teacherRepository) GetAll() ([]*domain.Teacher, error) {
    query := `
        SELECT t.id, t.user_id, t.employee_id, t.department, t.subject,
               t.qualification, t.experience, t.phone, t.hire_date,
               t.salary, t.created_at, t.updated_at,
               u.email, u.first_name, u.last_name, u.role, u.is_active
        FROM teachers t
        JOIN users u ON t.user_id = u.id
        ORDER BY t.created_at DESC`
    
    rows, err := r.db.Query(query)
    if err != nil {
        return nil, fmt.Errorf("failed to get all teachers: %w", err)
    }
    defer rows.Close()

    var teachers []*domain.Teacher
    for rows.Next() {
        teacher := &domain.Teacher{User: &domain.User{}}
        err := rows.Scan(
            &teacher.ID, &teacher.UserID, &teacher.EmployeeID, &teacher.Department,
            &teacher.Subject, &teacher.Qualification, &teacher.Experience,
            &teacher.Phone, &teacher.HireDate, &teacher.Salary,
            &teacher.CreatedAt, &teacher.UpdatedAt,
            &teacher.User.Email, &teacher.User.FirstName, &teacher.User.LastName,
            &teacher.User.Role, &teacher.User.IsActive,
        )
        if err != nil {
            return nil, fmt.Errorf("failed to scan teacher: %w", err)
        }
        teacher.User.ID = teacher.UserID
        teachers = append(teachers, teacher)
    }
    
    return teachers, nil
}

func (r *teacherRepository) Update(teacher *domain.Teacher) error {
    query := `
        UPDATE teachers 
        SET employee_id = $1, department = $2, subject = $3, qualification = $4,
            experience = $5, phone = $6, salary = $7, updated_at = $8
        WHERE id = $9`
    
    teacher.UpdatedAt = time.Now()
    
    _, err := r.db.Exec(query, teacher.EmployeeID, teacher.Department,
        teacher.Subject, teacher.Qualification, teacher.Experience,
        teacher.Phone, teacher.Salary, teacher.UpdatedAt, teacher.ID)
    
    if err != nil {
        return fmt.Errorf("failed to update teacher: %w", err)
    }
    
    return nil
}

func (r *teacherRepository) Delete(id int) error {
    query := `DELETE FROM teachers WHERE id = $1`
    
    _, err := r.db.Exec(query, id)
    if err != nil {
        return fmt.Errorf("failed to delete teacher: %w", err)
    }
    
    return nil
}
