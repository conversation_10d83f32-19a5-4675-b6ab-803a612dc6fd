package api

import (
    "github.com/go-chi/chi/v5"
)

func (router *Router) setupTeacherRoutes(r chi.Router, deps *Dependencies) {
    r.Route("/teachers", func(r chi.Router) {
        r.Get("/", deps.TeacherHandler.GetTeachers)
        r.Post("/", deps.TeacherHandler.CreateTeacher)
        r.Get("/{id}", deps.TeacherHandler.GetTeacher)
        r.Put("/{id}", deps.TeacherHandler.UpdateTeacher)
        r.Delete("/{id}", deps.TeacherHandler.DeleteTeacher)
        
        // Additional teacher-specific routes can be added here
        // r.Get("/{id}/classes", deps.TeacherHandler.GetTeacherClasses)
        // r.Get("/{id}/schedule", deps.TeacherHandler.GetTeacherSchedule)
    })
}
