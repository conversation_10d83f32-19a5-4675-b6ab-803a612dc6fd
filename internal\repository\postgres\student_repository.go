package postgres

import (
    "database/sql"
    "fmt"
    "time"

    "school-management-system/internal/domain"
)

type studentRepository struct {
    db *sql.DB
}

func NewStudentRepository(db *sql.DB) *studentRepository {
    return &studentRepository{db: db}
}

func (r *studentRepository) Create(student *domain.Student) error {
    query := `
        INSERT INTO students (user_id, student_id, grade, class, date_of_birth, address, 
                             parent_name, parent_phone, parent_email, enrollment_date, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        RETURNING id`
    
    now := time.Now()
    student.CreatedAt = now
    student.UpdatedAt = now
    student.EnrollmentDate = now

    err := r.db.QueryRow(query, student.UserID, student.StudentID, student.Grade,
        student.Class, student.DateOfBirth, student.Address, student.ParentName,
        student.<PERSON>rent<PERSON><PERSON>, student.ParentEmail, student.EnrollmentDate,
        student.CreatedAt, student.UpdatedAt).Scan(&student.ID)
    
    if err != nil {
        return fmt.Errorf("failed to create student: %w", err)
    }
    
    return nil
}

func (r *studentRepository) GetByID(id int) (*domain.Student, error) {
    query := `
        SELECT s.id, s.user_id, s.student_id, s.grade, s.class, s.date_of_birth,
               s.address, s.parent_name, s.parent_phone, s.parent_email,
               s.enrollment_date, s.created_at, s.updated_at,
               u.email, u.first_name, u.last_name, u.role, u.is_active
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE s.id = $1`
    
    student := &domain.Student{User: &domain.User{}}
    err := r.db.QueryRow(query, id).Scan(
        &student.ID, &student.UserID, &student.StudentID, &student.Grade,
        &student.Class, &student.DateOfBirth, &student.Address,
        &student.ParentName, &student.ParentPhone, &student.ParentEmail,
        &student.EnrollmentDate, &student.CreatedAt, &student.UpdatedAt,
        &student.User.Email, &student.User.FirstName, &student.User.LastName,
        &student.User.Role, &student.User.IsActive,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get student by ID: %w", err)
    }
    
    student.User.ID = student.UserID
    return student, nil
}

func (r *studentRepository) GetByUserID(userID int) (*domain.Student, error) {
    query := `
        SELECT id, user_id, student_id, grade, class, date_of_birth, address,
               parent_name, parent_phone, parent_email, enrollment_date, created_at, updated_at
        FROM students WHERE user_id = $1`
    
    student := &domain.Student{}
    err := r.db.QueryRow(query, userID).Scan(
        &student.ID, &student.UserID, &student.StudentID, &student.Grade,
        &student.Class, &student.DateOfBirth, &student.Address,
        &student.ParentName, &student.ParentPhone, &student.ParentEmail,
        &student.EnrollmentDate, &student.CreatedAt, &student.UpdatedAt,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get student by user ID: %w", err)
    }
    
    return student, nil
}

func (r *studentRepository) GetAll() ([]*domain.Student, error) {
    query := `
        SELECT s.id, s.user_id, s.student_id, s.grade, s.class, s.date_of_birth,
               s.address, s.parent_name, s.parent_phone, s.parent_email,
               s.enrollment_date, s.created_at, s.updated_at,
               u.email, u.first_name, u.last_name, u.role, u.is_active
        FROM students s
        JOIN users u ON s.user_id = u.id
        ORDER BY s.created_at DESC`
    
    rows, err := r.db.Query(query)
    if err != nil {
        return nil, fmt.Errorf("failed to get all students: %w", err)
    }
    defer rows.Close()

    var students []*domain.Student
    for rows.Next() {
        student := &domain.Student{User: &domain.User{}}
        err := rows.Scan(
            &student.ID, &student.UserID, &student.StudentID, &student.Grade,
            &student.Class, &student.DateOfBirth, &student.Address,
            &student.ParentName, &student.ParentPhone, &student.ParentEmail,
            &student.EnrollmentDate, &student.CreatedAt, &student.UpdatedAt,
            &student.User.Email, &student.User.FirstName, &student.User.LastName,
            &student.User.Role, &student.User.IsActive,
        )
        if err != nil {
            return nil, fmt.Errorf("failed to scan student: %w", err)
        }
        student.User.ID = student.UserID
        students = append(students, student)
    }
    
    return students, nil
}

func (r *studentRepository) Update(student *domain.Student) error {
    query := `
        UPDATE students 
        SET student_id = $1, grade = $2, class = $3, date_of_birth = $4,
            address = $5, parent_name = $6, parent_phone = $7, parent_email = $8, updated_at = $9
        WHERE id = $10`
    
    student.UpdatedAt = time.Now()
    
    _, err := r.db.Exec(query, student.StudentID, student.Grade, student.Class,
        student.DateOfBirth, student.Address, student.ParentName,
        student.ParentPhone, student.ParentEmail, student.UpdatedAt, student.ID)
    
    if err != nil {
        return fmt.Errorf("failed to update student: %w", err)
    }
    
    return nil
}

func (r *studentRepository) Delete(id int) error {
    query := `DELETE FROM students WHERE id = $1`
    
    _, err := r.db.Exec(query, id)
    if err != nil {
        return fmt.Errorf("failed to delete student: %w", err)
    }
    
    return nil
}
