// ===== internal/handler/student_handler.go (Updated) =====
package handler

import (
    "context"
    "encoding/json"
    "fmt"
    "net/http"
    "strconv"
    "time"

    "school-management-system/internal/domain"
    "school-management-system/internal/events"
    "school-management-system/internal/service"

    "github.com/go-chi/chi/v5"
    "github.com/go-chi/render"
)

type StudentHandler struct {
    studentService *service.StudentService
    eventBus       *events.EventBus
}

func NewStudentHandler(studentService *service.StudentService, eventBus *events.EventBus) *StudentHandler {
    return &StudentHandler{
        studentService: studentService,
        eventBus:       eventBus,
    }
}

func (h *StudentHandler) CreateStudent(w http.ResponseWriter, r *http.Request) {
    var req domain.CreateStudentRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    student, err := h.studentService.CreateStudent(&req)
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    // Publish student enrollment event
    event := events.Event{
        ID:     fmt.Sprintf("student_enrolled_%d_%d", student.ID, time.Now().Unix()),
        Type:   events.StudentEnrolled,
        UserID: student.UserID,
        Data: map[string]interface{}{
            "student_id":   student.ID,
            "student_name": fmt.Sprintf("%s %s", student.User.FirstName, student.User.LastName),
            "grade":        student.Grade,
            "class":        student.Class,
        },
        Timestamp: time.Now(),
    }

    // Publish event asynchronously
    go func() {
        if err := h.eventBus.Publish(context.Background(), event); err != nil {
            // Log error but don't fail the request
            fmt.Printf("Failed to publish student enrollment event: %v\n", err)
        }
    }()

    render.JSON(w, r, student)
    // go.mod
module school-management-system

go 1.21

require (
    github.com/go-chi/chi/v5 v5.0.10
    github.com/go-chi/cors v1.2.1
    github.com/go-chi/jwtauth/v5 v5.1.1
    github.com/golang-jwt/jwt/v5 v5.0.0
    github.com/gorilla/websocket v1.5.0
    github.com/lib/pq v1.10.9
    github.com/joho/godotenv v1.4.0
    golang.org/x/crypto v0.14.0
    gopkg.in/gomail.v2 v2.0.0-20160411212932-81ebce5c23df
)

// ===== PROJECT STRUCTURE (UPDATED) =====
// school-management-system/
// ├── cmd/
// │   └── server/
// │       └── main.go
// ├── internal/
// │   ├── api/
// │   │   ├── router.go
// │   │   ├── user_routes.go
// │   │   ├── student_routes.go
// │   │   ├── teacher_routes.go
// │   │   └── websocket_routes.go
// │   ├── config/
// │   │   └── config.go
// │   ├── domain/
// │   │   ├── user.go
// │   │   ├── student.go
// │   │   └── teacher.go
// │   ├── repository/
// │   │   ├── interfaces.go
// │   │   └── postgres/
// │   │       ├── user_repository.go
// │   │       ├── student_repository.go
// │   │       └── teacher_repository.go
// │   ├── service/
// │   │   ├── auth_service.go
// │   │   ├── user_service.go
// │   │   ├── student_service.go
// │   │   └── teacher_service.go
// │   ├── handler/
// │   │   ├── auth_handler.go
// │   │   ├── user_handler.go
// │   │   ├── student_handler.go
// │   │   └── teacher_handler.go
// │   ├── middleware/
// │   │   └── auth_middleware.go
// │   ├── events/
// │   │   └── event_bus.go
// │   ├── websocket/
// │   │   └── hub.go
// │   ├── background/
// │   │   └── jobs.go
// │   ├── notification/
// │   │   └── email_service.go
// │   ├── cache/
// │   │   └── redis_cache.go
// │   └── database/
// │       └── postgres.go
// ├── migrations/
// │   └── 001_initial_schema.sql
// ├── .env.example
// ├── docker-compose.yml
// ├── Dockerfile
// ├── Makefile
// └── README.md

// ===== cmd/server/main.go =====
package main

import (
    "log"
    "net/http"
    "os"

    "school-management-system/internal/api"
    "school-management-system/internal/config"
    "school-management-system/internal/database"

    "github.com/joho/godotenv"
)

func main() {
    // Load environment variables
    if err := godotenv.Load(); err != nil {
        log.Printf("Warning: .env file not found: %v", err)
    }

    // Load configuration
    cfg := config.Load()

    // Initialize database
    db, err := database.NewPostgresDB(cfg.DatabaseURL)
    if err != nil {
        log.Fatal("Failed to connect to database:", err)
    }
    defer db.Close()

    // Initialize and setup API router
    router := api.NewRouter(db, cfg)

    port := os.Getenv("PORT")
    if port == "" {
        port = "8080"
    }

    log.Printf("Server starting on port %s", port)
    log.Fatal(http.ListenAndServe(":"+port, router))
}

// ===== internal/api/router.go =====
package api

import (
    "database/sql"

    "school-management-system/internal/background"
    "school-management-system/internal/config"
    "school-management-system/internal/events"
    "school-management-system/internal/handler"
    "school-management-system/internal/middleware"
    "school-management-system/internal/repository/postgres"
    "school-management-system/internal/service"
    "school-management-system/internal/websocket"

    "github.com/go-chi/chi/v5"
    chiMiddleware "github.com/go-chi/chi/v5/middleware"
    "github.com/go-chi/cors"
)

type Router struct {
    db  *sql.DB
    cfg *config.Config
}

func NewRouter(db *sql.DB, cfg *config.Config) *chi.Mux {
    router := &Router{
        db:  db,
        cfg: cfg,
    }

    return router.setupRoutes()
}

func (router *Router) setupRoutes() *chi.Mux {
    r := chi.NewRouter()

    // Global middleware
    r.Use(chiMiddleware.Logger)
    r.Use(chiMiddleware.Recoverer)
    r.Use(cors.Handler(cors.Options{
        AllowedOrigins:   []string{"*"},
        AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
        AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token"},
        ExposedHeaders:   []string{"Link"},
        AllowCredentials: true,
        MaxAge:           300,
    }))

    // Initialize dependencies
    deps := router.initializeDependencies()

    // Start background services
    deps.JobManager.Start()

    // Setup API routes
    r.Route("/api/v1", func(r chi.Router) {
        // Public routes
        router.setupPublicRoutes(r, deps)
        
        // WebSocket routes (can be public or protected based on your needs)
        router.setupWebSocketRoutes(r, deps)
        
        // Protected routes
        router.setupProtectedRoutes(r, deps)
    })

    return r
}

type Dependencies struct {
    // Core Services
    AuthService    *service.AuthService
    UserService    *service.UserService
    StudentService *service.StudentService
    TeacherService *service.TeacherService

    // Handlers
    AuthHandler    *handler.AuthHandler
    UserHandler    *handler.UserHandler
    StudentHandler *handler.StudentHandler
    TeacherHandler *handler.TeacherHandler

    // Middleware
    AuthMiddleware *middleware.AuthMiddleware

    // Event System
    EventBus *events.EventBus

    // WebSocket
    WebSocketHub *websocket.Hub

    // Background Jobs
    JobManager *background.JobManager
}

func (router *Router) initializeDependencies() *Dependencies {
    // Initialize repositories
    userRepo := postgres.NewUserRepository(router.db)
    studentRepo := postgres.NewStudentRepository(router.db)
    teacherRepo := postgres.NewTeacherRepository(router.db)

    // Initialize services
    authService := service.NewAuthService(userRepo, router.cfg.JWTSecret)
    userService := service.NewUserService(userRepo)
    studentService := service.NewStudentService(studentRepo)
    teacherService := service.NewTeacherService(teacherRepo)

    // Initialize event system
    eventBus := events.NewEventBus(1000, 5) // 1000 buffer, 5 workers

    // Initialize WebSocket hub
    webSocketHub := websocket.NewHub(eventBus)

    // Initialize background job manager
    jobManager := background.NewJobManager(eventBus, studentService, teacherService)

    // Initialize handlers
    authHandler := handler.NewAuthHandler(authService)
    userHandler := handler.NewUserHandler(userService)
    studentHandler := handler.NewStudentHandler(studentService, eventBus) // Pass eventBus for publishing events
    teacherHandler := handler.NewTeacherHandler(teacherService, eventBus)

    // Initialize middleware
    authMiddleware := middleware.NewAuthMiddleware(router.cfg.JWTSecret)

    return &Dependencies{
        AuthService:    authService,
        UserService:    userService,
        StudentService: studentService,
        TeacherService: teacherService,
        AuthHandler:    authHandler,
        UserHandler:    userHandler,
        StudentHandler: studentHandler,
        TeacherHandler: teacherHandler,
        AuthMiddleware: authMiddleware,
        EventBus:       eventBus,
        WebSocketHub:   webSocketHub,
        JobManager:     jobManager,
    }
}

func (router *Router) setupPublicRoutes(r chi.Router, deps *Dependencies) {
    // Authentication routes
    r.Post("/auth/login", deps.AuthHandler.Login)
    r.Post("/auth/register", deps.AuthHandler.Register)

    // Health check
    r.Get("/health", func(w http.ResponseWriter, r *http.Request) {
        w.WriteHeader(200)
        w.Write([]byte("OK"))
    })
}

func (router *Router) setupProtectedRoutes(r chi.Router, deps *Dependencies) {
    r.Group(func(r chi.Router) {
        r.Use(deps.AuthMiddleware.Authenticate)

        // User routes
        router.setupUserRoutes(r, deps)
        
        // Student routes
        router.setupStudentRoutes(r, deps)
        
        // Teacher routes  
        router.setupTeacherRoutes(r, deps)
    })
}

// ===== internal/api/user_routes.go =====
package api

import (
    "github.com/go-chi/chi/v5"
)

func (router *Router) setupUserRoutes(r chi.Router, deps *Dependencies) {
    r.Route("/users", func(r chi.Router) {
        r.Get("/", deps.UserHandler.GetUsers)
        r.Get("/{id}", deps.UserHandler.GetUser)
        r.Put("/{id}", deps.UserHandler.UpdateUser)
        r.Delete("/{id}", deps.UserHandler.DeleteUser)
    })
}

// ===== internal/api/student_routes.go =====
package api

import (
    "github.com/go-chi/chi/v5"
)

func (router *Router) setupStudentRoutes(r chi.Router, deps *Dependencies) {
    r.Route("/students", func(r chi.Router) {
        r.Get("/", deps.StudentHandler.GetStudents)
        r.Post("/", deps.StudentHandler.CreateStudent)
        r.Get("/{id}", deps.StudentHandler.GetStudent)
        r.Put("/{id}", deps.StudentHandler.UpdateStudent)
        r.Delete("/{id}", deps.StudentHandler.DeleteStudent)
        
        // Additional student-specific routes can be added here
        // r.Get("/{id}/grades", deps.StudentHandler.GetStudentGrades)
        // r.Get("/{id}/attendance", deps.StudentHandler.GetStudentAttendance)
    })
}

// ===== internal/api/teacher_routes.go =====
package api

import (
    "github.com/go-chi/chi/v5"
)

// ===== internal/events/event_bus.go =====
package events

import (
    "context"
    "fmt"
    "log"
    "sync"
    "time"
)

type EventType string

const (
    StudentEnrolled    EventType = "student.enrolled"
    StudentUpdated     EventType = "student.updated"
    TeacherHired       EventType = "teacher.hired"
    UserLoggedIn       EventType = "user.logged_in"
    GradeSubmitted     EventType = "grade.submitted"
    AttendanceMarked   EventType = "attendance.marked"
)

type Event struct {
    ID        string                 `json:"id"`
    Type      EventType              `json:"type"`
    Data      map[string]interface{} `json:"data"`
    UserID    int                    `json:"user_id"`
    Timestamp time.Time              `json:"timestamp"`
}

type Handler func(ctx context.Context, event Event) error

type EventBus struct {
    handlers map[EventType][]Handler
    mu       sync.RWMutex
    buffer   chan Event
    workers  int
    ctx      context.Context
    cancel   context.CancelFunc
    wg       sync.WaitGroup
}

func NewEventBus(bufferSize, workers int) *EventBus {
    ctx, cancel := context.WithCancel(context.Background())
    
    bus := &EventBus{
        handlers: make(map[EventType][]Handler),
        buffer:   make(chan Event, bufferSize),
        workers:  workers,
        ctx:      ctx,
        cancel:   cancel,
    }
    
    // Start worker goroutines
    for i := 0; i < workers; i++ {
        bus.wg.Add(1)
        go bus.worker(i)
    }
    
    return bus
}

func (bus *EventBus) Subscribe(eventType EventType, handler Handler) {
    bus.mu.Lock()
    defer bus.mu.Unlock()
    
    bus.handlers[eventType] = append(bus.handlers[eventType], handler)
    log.Printf("Handler subscribed to event type: %s", eventType)
}

func (bus *EventBus) Publish(ctx context.Context, event Event) error {
    select {
    case bus.buffer <- event:
        return nil
    case <-ctx.Done():
        return ctx.Err()
    case <-time.After(5 * time.Second):
        return fmt.Errorf("failed to publish event: buffer full")
    }
}

func (bus *EventBus) worker(id int) {
    defer bus.wg.Done()
    log.Printf("Event worker %d started", id)
    
    for {
        select {
        case event := <-bus.buffer:
            bus.handleEvent(event)
        case <-bus.ctx.Done():
            log.Printf("Event worker %d shutting down", id)
            return
        }
    }
}

func (bus *EventBus) handleEvent(event Event) {
    bus.mu.RLock()
    handlers := bus.handlers[event.Type]
    bus.mu.RUnlock()
    
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    for _, handler := range handlers {
        go func(h Handler) {
            if err := h(ctx, event); err != nil {
                log.Printf("Error handling event %s: %v", event.Type, err)
            }
        }(handler)
    }
}

func (bus *EventBus) Close() {
    bus.cancel()
    bus.wg.Wait()
    close(bus.buffer)
    log.Println("EventBus closed")
}

// ===== internal/websocket/hub.go =====
package websocket

import (
    "context"
    "encoding/json"
    "log"
    "net/http"
    "sync"
    "time"

    "school-management-system/internal/events"

    "github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
    ReadBufferSize:  1024,
    WriteBufferSize: 1024,
    CheckOrigin: func(r *http.Request) bool {
        return true // Allow all origins in development
    },
}

type Client struct {
    ID     string
    UserID int
    Role   string
    Hub    *Hub
    Conn   *websocket.Conn
    Send   chan []byte
}

type Hub struct {
    clients    map[*Client]bool
    broadcast  chan []byte
    register   chan *Client
    unregister chan *Client
    mu         sync.RWMutex
    eventBus   *events.EventBus
}

func NewHub(eventBus *events.EventBus) *Hub {
    hub := &Hub{
        clients:    make(map[*Client]bool),
        broadcast:  make(chan []byte, 256),
        register:   make(chan *Client),
        unregister: make(chan *Client),
        eventBus:   eventBus,
    }
    
    // Subscribe to events for real-time notifications
    eventBus.Subscribe(events.StudentEnrolled, hub.handleStudentEvent)
    eventBus.Subscribe(events.StudentUpdated, hub.handleStudentEvent)
    eventBus.Subscribe(events.TeacherHired, hub.handleTeacherEvent)
    eventBus.Subscribe(events.GradeSubmitted, hub.handleGradeEvent)
    
    go hub.run()
    return hub
}

func (h *Hub) run() {
    for {
        select {
        case client := <-h.register:
            h.mu.Lock()
            h.clients[client] = true
            h.mu.Unlock()
            log.Printf("Client %s connected (User: %d, Role: %s)", client.ID, client.UserID, client.Role)
            
        case client := <-h.unregister:
            h.mu.Lock()
            if _, ok := h.clients[client]; ok {
                delete(h.clients, client)
                close(client.Send)
            }
            h.mu.Unlock()
            log.Printf("Client %s disconnected", client.ID)
            
        case message := <-h.broadcast:
            h.mu.RLock()
            for client := range h.clients {
                select {
                case client.Send <- message:
                default:
                    delete(h.clients, client)
                    close(client.Send)
                }
            }
            h.mu.RUnlock()
        }
    }
}

// Event handlers for real-time notifications
func (h *Hub) handleStudentEvent(ctx context.Context, event events.Event) error {
    message := map[string]interface{}{
        "type":      "student_notification",
        "event":     event.Type,
        "data":      event.Data,
        "timestamp": event.Timestamp,
    }
    
    return h.broadcastToRole(message, "admin", "teacher")
}

func (h *Hub) handleTeacherEvent(ctx context.Context, event events.Event) error {
    message := map[string]interface{}{
        "type":      "teacher_notification",
        "event":     event.Type,
        "data":      event.Data,
        "timestamp": event.Timestamp,
    }
    
    return h.broadcastToRole(message, "admin")
}

func (h *Hub) handleGradeEvent(ctx context.Context, event events.Event) error {
    message := map[string]interface{}{
        "type":      "grade_notification",
        "event":     event.Type,
        "data":      event.Data,
        "timestamp": event.Timestamp,
    }
    
    // Notify the specific student and admins/teachers
    if studentID, ok := event.Data["student_id"].(int); ok {
        return h.broadcastToUserAndRoles(message, studentID, "admin", "teacher")
    }
    
    return h.broadcastToRole(message, "admin", "teacher")
}

func (h *Hub) broadcastToRole(message map[string]interface{}, roles ...string) error {
    data, err := json.Marshal(message)
    if err != nil {
        return err
    }
    
    h.mu.RLock()
    defer h.mu.RUnlock()
    
    roleMap := make(map[string]bool)
    for _, role := range roles {
        roleMap[role] = true
    }
    
    for client := range h.clients {
        if roleMap[client.Role] {
            select {
            case client.Send <- data:
            default:
                delete(h.clients, client)
                close(client.Send)
            }
        }
    }
    
    return nil
}

func (h *Hub) broadcastToUserAndRoles(message map[string]interface{}, userID int, roles ...string) error {
    data, err := json.Marshal(message)
    if err != nil {
        return err
    }
    
    h.mu.RLock()
    defer h.mu.RUnlock()
    
    roleMap := make(map[string]bool)
    for _, role := range roles {
        roleMap[role] = true
    }
    
    for client := range h.clients {
        if client.UserID == userID || roleMap[client.Role] {
            select {
            case client.Send <- data:
            default:
                delete(h.clients, client)
                close(client.Send)
            }
        }
    }
    
    return nil
}

// WebSocket handler
func (h *Hub) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
    conn, err := upgrader.Upgrade(w, r, nil)
    if err != nil {
        log.Printf("WebSocket upgrade error: %v", err)
        return
    }
    
    // Get user info from JWT token (you'd implement this)
    userID, role := h.getUserFromRequest(r)
    
    client := &Client{
        ID:     generateClientID(),
        UserID: userID,
        Role:   role,
        Hub:    h,
        Conn:   conn,
        Send:   make(chan []byte, 256),
    }
    
    client.Hub.register <- client
    
    // Start goroutines for reading and writing
    go client.writePump()
    go client.readPump()
}

func (c *Client) readPump() {
    defer func() {
        c.Hub.unregister <- c
        c.Conn.Close()
    }()
    
    c.Conn.SetReadLimit(512)
    c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
    c.Conn.SetPongHandler(func(string) error {
        c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
        return nil
    })
    
    for {
        _, _, err := c.Conn.ReadMessage()
        if err != nil {
            break
        }
    }
}

func (c *Client) writePump() {
    ticker := time.NewTicker(54 * time.Second)
    defer func() {
        ticker.Stop()
        c.Conn.Close()
    }()
    
    for {
        select {
        case message, ok := <-c.Send:
            c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
            if !ok {
                c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
                return
            }
            
            if err := c.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
                return
            }
            
        case <-ticker.C:
            c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
            if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
                return
            }
        }
    }
}

func (h *Hub) getUserFromRequest(r *http.Request) (int, string) {
    // Extract user info from JWT token or session
    // This is a placeholder - implement based on your auth system
    return 1, "admin"
}

func generateClientID() string {
    return fmt.Sprintf("client_%d", time.Now().UnixNano())
}

// Add missing fmt import
import "fmt"

// ===== internal/background/jobs.go =====
package background

import (
    "context"
    "log"
    "sync"
    "time"

    "school-management-system/internal/events"
    "school-management-system/internal/service"
)

type JobManager struct {
    eventBus       *events.EventBus
    studentService *service.StudentService
    teacherService *service.TeacherService
    ctx            context.Context
    cancel         context.CancelFunc
    wg             sync.WaitGroup
}

func NewJobManager(eventBus *events.EventBus, studentService *service.StudentService, teacherService *service.TeacherService) *JobManager {
    ctx, cancel := context.WithCancel(context.Background())
    
    return &JobManager{
        eventBus:       eventBus,
        studentService: studentService,
        teacherService: teacherService,
        ctx:            ctx,
        cancel:         cancel,
    }
}

func (jm *JobManager) Start() {
    // Start daily cleanup job
    jm.wg.Add(1)
    go jm.dailyCleanupJob()
    
    // Start attendance reminder job
    jm.wg.Add(1)
    go jm.attendanceReminderJob()
    
    // Start grade calculation job
    jm.wg.Add(1)
    go jm.gradeCalculationJob()
    
    log.Println("Background job manager started")
}

func (jm *JobManager) Stop() {
    jm.cancel()
    jm.wg.Wait()
    log.Println("Background job manager stopped")
}

func (jm *JobManager) dailyCleanupJob() {
    defer jm.wg.Done()
    
    ticker := time.NewTicker(24 * time.Hour)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            log.Println("Running daily cleanup job...")
            if err := jm.performDailyCleanup(); err != nil {
                log.Printf("Daily cleanup job failed: %v", err)
            }
        case <-jm.ctx.Done():
            return
        }
    }
}

func (jm *JobManager) attendanceReminderJob() {
    defer jm.wg.Done()
    
    // Run every hour during school hours (8 AM - 6 PM)
    ticker := time.NewTicker(1 * time.Hour)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            now := time.Now()
            if now.Hour() >= 8 && now.Hour() <= 18 {
                log.Println("Checking attendance reminders...")
                if err := jm.sendAttendanceReminders(); err != nil {
                    log.Printf("Attendance reminder job failed: %v", err)
                }
            }
        case <-jm.ctx.Done():
            return
        }
    }
}

func (jm *JobManager) gradeCalculationJob() {
    defer jm.wg.Done()
    
    // Run at midnight every day
    ticker := time.NewTicker(1 * time.Hour)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            now := time.Now()
            if now.Hour() == 0 { // Midnight
                log.Println("Running grade calculations...")
                if err := jm.calculateGrades(); err != nil {
                    log.Printf("Grade calculation job failed: %v", err)
                }
            }
        case <-jm.ctx.Done():
            return
        }
    }
}

func (jm *JobManager) performDailyCleanup() error {
    // Clean up old sessions, temporary files, etc.
    log.Println("Performing daily cleanup tasks")
    return nil
}

func (jm *JobManager) sendAttendanceReminders() error {
    // Send reminders to teachers about attendance
    event := events.Event{
        ID:        generateEventID(),
        Type:      "attendance.reminder",
        Data:      map[string]interface{}{"message": "Please mark attendance"},
        Timestamp: time.Now(),
    }
    
    return jm.eventBus.Publish(context.Background(), event)
}

func (jm *JobManager) calculateGrades() error {
    // Calculate and update student grades
    log.Println("Calculating student grades")
    return nil
}

func generateEventID() string {
    return fmt.Sprintf("event_%d", time.Now().UnixNano())
}

// ===== internal/api/websocket_routes.go =====
package api

import (
    "net/http"

    "github.com/go-chi/chi/v5"
)

func (router *Router) setupWebSocketRoutes(r chi.Router, deps *Dependencies) {
    r.Route("/ws", func(r chi.Router) {
        r.Get("/", func(w http.ResponseWriter, r *http.Request) {
            deps.WebSocketHub.HandleWebSocket(w, r)
        })
    })
}

// ===== internal/config/config.go =====
package config

import (
    "os"
)

type Config struct {
    DatabaseURL string
    JWTSecret   string
    Port        string
}

func Load() *Config {
    return &Config{
        DatabaseURL: getEnv("DATABASE_URL", "postgres://user:password@localhost/school_db?sslmode=disable"),
        JWTSecret:   getEnv("JWT_SECRET", "your-secret-key"),
        Port:        getEnv("PORT", "8080"),
    }
}

func getEnv(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}

// ===== internal/domain/user.go =====
package domain

import (
    "time"
)

type UserRole string

const (
    RoleAdmin   UserRole = "admin"
    RoleTeacher UserRole = "teacher"
    RoleStudent UserRole = "student"
)

type User struct {
    ID        int       `json:"id" db:"id"`
    Email     string    `json:"email" db:"email"`
    Password  string    `json:"-" db:"password"`
    FirstName string    `json:"first_name" db:"first_name"`
    LastName  string    `json:"last_name" db:"last_name"`
    Role      UserRole  `json:"role" db:"role"`
    IsActive  bool      `json:"is_active" db:"is_active"`
    CreatedAt time.Time `json:"created_at" db:"created_at"`
    UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

type LoginRequest struct {
    Email    string `json:"email"`
    Password string `json:"password"`
}

type RegisterRequest struct {
    Email     string   `json:"email"`
    Password  string   `json:"password"`
    FirstName string   `json:"first_name"`
    LastName  string   `json:"last_name"`
    Role      UserRole `json:"role"`
}

type LoginResponse struct {
    Token string `json:"token"`
    User  User   `json:"user"`
}

// ===== internal/domain/student.go =====
package domain

import (
    "time"
)

type Student struct {
    ID           int       `json:"id" db:"id"`
    UserID       int       `json:"user_id" db:"user_id"`
    StudentID    string    `json:"student_id" db:"student_id"`
    Grade        string    `json:"grade" db:"grade"`
    Class        string    `json:"class" db:"class"`
    DateOfBirth  time.Time `json:"date_of_birth" db:"date_of_birth"`
    Address      string    `json:"address" db:"address"`
    ParentName   string    `json:"parent_name" db:"parent_name"`
    ParentPhone  string    `json:"parent_phone" db:"parent_phone"`
    ParentEmail  string    `json:"parent_email" db:"parent_email"`
    EnrollmentDate time.Time `json:"enrollment_date" db:"enrollment_date"`
    CreatedAt    time.Time `json:"created_at" db:"created_at"`
    UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
    
    // Embedded user information
    User *User `json:"user,omitempty"`
}

type CreateStudentRequest struct {
    UserID       int       `json:"user_id"`
    StudentID    string    `json:"student_id"`
    Grade        string    `json:"grade"`
    Class        string    `json:"class"`
    DateOfBirth  time.Time `json:"date_of_birth"`
    Address      string    `json:"address"`
    ParentName   string    `json:"parent_name"`
    ParentPhone  string    `json:"parent_phone"`
    ParentEmail  string    `json:"parent_email"`
}

// ===== internal/domain/teacher.go =====
package domain

import (
    "time"
)

type Teacher struct {
    ID          int       `json:"id" db:"id"`
    UserID      int       `json:"user_id" db:"user_id"`
    EmployeeID  string    `json:"employee_id" db:"employee_id"`
    Department  string    `json:"department" db:"department"`
    Subject     string    `json:"subject" db:"subject"`
    Qualification string  `json:"qualification" db:"qualification"`
    Experience  int       `json:"experience" db:"experience"`
    Phone       string    `json:"phone" db:"phone"`
    HireDate    time.Time `json:"hire_date" db:"hire_date"`
    Salary      float64   `json:"salary" db:"salary"`
    CreatedAt   time.Time `json:"created_at" db:"created_at"`
    UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
    
    // Embedded user information
    User *User `json:"user,omitempty"`
}

type CreateTeacherRequest struct {
    UserID        int     `json:"user_id"`
    EmployeeID    string  `json:"employee_id"`
    Department    string  `json:"department"`
    Subject       string  `json:"subject"`
    Qualification string  `json:"qualification"`
    Experience    int     `json:"experience"`
    Phone         string  `json:"phone"`
    Salary        float64 `json:"salary"`
}

// ===== internal/repository/interfaces.go =====
package repository

import (
    "school-management-system/internal/domain"
)

type UserRepository interface {
    Create(user *domain.User) error
    GetByID(id int) (*domain.User, error)
    GetByEmail(email string) (*domain.User, error)
    GetAll() ([]*domain.User, error)
    Update(user *domain.User) error
    Delete(id int) error
}

type StudentRepository interface {
    Create(student *domain.Student) error
    GetByID(id int) (*domain.Student, error)
    GetByUserID(userID int) (*domain.Student, error)
    GetAll() ([]*domain.Student, error)
    Update(student *domain.Student) error
    Delete(id int) error
}

type TeacherRepository interface {
    Create(teacher *domain.Teacher) error
    GetByID(id int) (*domain.Teacher, error)
    GetByUserID(userID int) (*domain.Teacher, error)
    GetAll() ([]*domain.Teacher, error)
    Update(teacher *domain.Teacher) error
    Delete(id int) error
}

// ===== internal/database/postgres.go =====
package database

import (
    "database/sql"
    "fmt"

    _ "github.com/lib/pq"
)

func NewPostgresDB(databaseURL string) (*sql.DB, error) {
    db, err := sql.Open("postgres", databaseURL)
    if err != nil {
        return nil, fmt.Errorf("failed to open database: %w", err)
    }

    if err := db.Ping(); err != nil {
        return nil, fmt.Errorf("failed to ping database: %w", err)
    }

    return db, nil
}

// ===== internal/repository/postgres/user_repository.go =====
package postgres

import (
    "database/sql"
    "fmt"
    "time"

    "school-management-system/internal/domain"
)

type userRepository struct {
    db *sql.DB
}

func NewUserRepository(db *sql.DB) *userRepository {
    return &userRepository{db: db}
}

func (r *userRepository) Create(user *domain.User) error {
    query := `
        INSERT INTO users (email, password, first_name, last_name, role, is_active, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id`
    
    now := time.Now()
    user.CreatedAt = now
    user.UpdatedAt = now
    user.IsActive = true

    err := r.db.QueryRow(query, user.Email, user.Password, user.FirstName, 
        user.LastName, user.Role, user.IsActive, user.CreatedAt, user.UpdatedAt).Scan(&user.ID)
    
    if err != nil {
        return fmt.Errorf("failed to create user: %w", err)
    }
    
    return nil
}

func (r *userRepository) GetByID(id int) (*domain.User, error) {
    query := `
        SELECT id, email, password, first_name, last_name, role, is_active, created_at, updated_at
        FROM users WHERE id = $1`
    
    user := &domain.User{}
    err := r.db.QueryRow(query, id).Scan(
        &user.ID, &user.Email, &user.Password, &user.FirstName,
        &user.LastName, &user.Role, &user.IsActive, &user.CreatedAt, &user.UpdatedAt,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get user by ID: %w", err)
    }
    
    return user, nil
}

func (r *userRepository) GetByEmail(email string) (*domain.User, error) {
    query := `
        SELECT id, email, password, first_name, last_name, role, is_active, created_at, updated_at
        FROM users WHERE email = $1`
    
    user := &domain.User{}
    err := r.db.QueryRow(query, email).Scan(
        &user.ID, &user.Email, &user.Password, &user.FirstName,
        &user.LastName, &user.Role, &user.IsActive, &user.CreatedAt, &user.UpdatedAt,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get user by email: %w", err)
    }
    
    return user, nil
}

func (r *userRepository) GetAll() ([]*domain.User, error) {
    query := `
        SELECT id, email, password, first_name, last_name, role, is_active, created_at, updated_at
        FROM users ORDER BY created_at DESC`
    
    rows, err := r.db.Query(query)
    if err != nil {
        return nil, fmt.Errorf("failed to get all users: %w", err)
    }
    defer rows.Close()

    var users []*domain.User
    for rows.Next() {
        user := &domain.User{}
        err := rows.Scan(&user.ID, &user.Email, &user.Password, &user.FirstName,
            &user.LastName, &user.Role, &user.IsActive, &user.CreatedAt, &user.UpdatedAt)
        if err != nil {
            return nil, fmt.Errorf("failed to scan user: %w", err)
        }
        users = append(users, user)
    }
    
    return users, nil
}

func (r *userRepository) Update(user *domain.User) error {
    query := `
        UPDATE users 
        SET email = $1, first_name = $2, last_name = $3, role = $4, is_active = $5, updated_at = $6
        WHERE id = $7`
    
    user.UpdatedAt = time.Now()
    
    _, err := r.db.Exec(query, user.Email, user.FirstName, user.LastName,
        user.Role, user.IsActive, user.UpdatedAt, user.ID)
    
    if err != nil {
        return fmt.Errorf("failed to update user: %w", err)
    }
    
    return nil
}

func (r *userRepository) Delete(id int) error {
    query := `DELETE FROM users WHERE id = $1`
    
    _, err := r.db.Exec(query, id)
    if err != nil {
        return fmt.Errorf("failed to delete user: %w", err)
    }
    
    return nil
}

// ===== internal/repository/postgres/student_repository.go =====
package postgres

import (
    "database/sql"
    "fmt"
    "time"

    "school-management-system/internal/domain"
)

type studentRepository struct {
    db *sql.DB
}

func NewStudentRepository(db *sql.DB) *studentRepository {
    return &studentRepository{db: db}
}

func (r *studentRepository) Create(student *domain.Student) error {
    query := `
        INSERT INTO students (user_id, student_id, grade, class, date_of_birth, address, 
                             parent_name, parent_phone, parent_email, enrollment_date, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        RETURNING id`
    
    now := time.Now()
    student.CreatedAt = now
    student.UpdatedAt = now
    student.EnrollmentDate = now

    err := r.db.QueryRow(query, student.UserID, student.StudentID, student.Grade,
        student.Class, student.DateOfBirth, student.Address, student.ParentName,
        student.ParentPhone, student.ParentEmail, student.EnrollmentDate,
        student.CreatedAt, student.UpdatedAt).Scan(&student.ID)
    
    if err != nil {
        return fmt.Errorf("failed to create student: %w", err)
    }
    
    return nil
}

func (r *studentRepository) GetByID(id int) (*domain.Student, error) {
    query := `
        SELECT s.id, s.user_id, s.student_id, s.grade, s.class, s.date_of_birth,
               s.address, s.parent_name, s.parent_phone, s.parent_email,
               s.enrollment_date, s.created_at, s.updated_at,
               u.email, u.first_name, u.last_name, u.role, u.is_active
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE s.id = $1`
    
    student := &domain.Student{User: &domain.User{}}
    err := r.db.QueryRow(query, id).Scan(
        &student.ID, &student.UserID, &student.StudentID, &student.Grade,
        &student.Class, &student.DateOfBirth, &student.Address,
        &student.ParentName, &student.ParentPhone, &student.ParentEmail,
        &student.EnrollmentDate, &student.CreatedAt, &student.UpdatedAt,
        &student.User.Email, &student.User.FirstName, &student.User.LastName,
        &student.User.Role, &student.User.IsActive,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get student by ID: %w", err)
    }
    
    student.User.ID = student.UserID
    return student, nil
}

func (r *studentRepository) GetByUserID(userID int) (*domain.Student, error) {
    query := `
        SELECT id, user_id, student_id, grade, class, date_of_birth, address,
               parent_name, parent_phone, parent_email, enrollment_date, created_at, updated_at
        FROM students WHERE user_id = $1`
    
    student := &domain.Student{}
    err := r.db.QueryRow(query, userID).Scan(
        &student.ID, &student.UserID, &student.StudentID, &student.Grade,
        &student.Class, &student.DateOfBirth, &student.Address,
        &student.ParentName, &student.ParentPhone, &student.ParentEmail,
        &student.EnrollmentDate, &student.CreatedAt, &student.UpdatedAt,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get student by user ID: %w", err)
    }
    
    return student, nil
}

func (r *studentRepository) GetAll() ([]*domain.Student, error) {
    query := `
        SELECT s.id, s.user_id, s.student_id, s.grade, s.class, s.date_of_birth,
               s.address, s.parent_name, s.parent_phone, s.parent_email,
               s.enrollment_date, s.created_at, s.updated_at,
               u.email, u.first_name, u.last_name, u.role, u.is_active
        FROM students s
        JOIN users u ON s.user_id = u.id
        ORDER BY s.created_at DESC`
    
    rows, err := r.db.Query(query)
    if err != nil {
        return nil, fmt.Errorf("failed to get all students: %w", err)
    }
    defer rows.Close()

    var students []*domain.Student
    for rows.Next() {
        student := &domain.Student{User: &domain.User{}}
        err := rows.Scan(
            &student.ID, &student.UserID, &student.StudentID, &student.Grade,
            &student.Class, &student.DateOfBirth, &student.Address,
            &student.ParentName, &student.ParentPhone, &student.ParentEmail,
            &student.EnrollmentDate, &student.CreatedAt, &student.UpdatedAt,
            &student.User.Email, &student.User.FirstName, &student.User.LastName,
            &student.User.Role, &student.User.IsActive,
        )
        if err != nil {
            return nil, fmt.Errorf("failed to scan student: %w", err)
        }
        student.User.ID = student.UserID
        students = append(students, student)
    }
    
    return students, nil
}

func (r *studentRepository) Update(student *domain.Student) error {
    query := `
        UPDATE students 
        SET student_id = $1, grade = $2, class = $3, date_of_birth = $4,
            address = $5, parent_name = $6, parent_phone = $7, parent_email = $8, updated_at = $9
        WHERE id = $10`
    
    student.UpdatedAt = time.Now()
    
    _, err := r.db.Exec(query, student.StudentID, student.Grade, student.Class,
        student.DateOfBirth, student.Address, student.ParentName,
        student.ParentPhone, student.ParentEmail, student.UpdatedAt, student.ID)
    
    if err != nil {
        return fmt.Errorf("failed to update student: %w", err)
    }
    
    return nil
}

func (r *studentRepository) Delete(id int) error {
    query := `DELETE FROM students WHERE id = $1`
    
    _, err := r.db.Exec(query, id)
    if err != nil {
        return fmt.Errorf("failed to delete student: %w", err)
    }
    
    return nil
}

// ===== internal/repository/postgres/teacher_repository.go =====
package postgres

import (
    "database/sql"
    "fmt"
    "time"

    "school-management-system/internal/domain"
)

type teacherRepository struct {
    db *sql.DB
}

func NewTeacherRepository(db *sql.DB) *teacherRepository {
    return &teacherRepository{db: db}
}

func (r *teacherRepository) Create(teacher *domain.Teacher) error {
    query := `
        INSERT INTO teachers (user_id, employee_id, department, subject, qualification,
                             experience, phone, hire_date, salary, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING id`
    
    now := time.Now()
    teacher.CreatedAt = now
    teacher.UpdatedAt = now
    teacher.HireDate = now

    err := r.db.QueryRow(query, teacher.UserID, teacher.EmployeeID,
        teacher.Department, teacher.Subject, teacher.Qualification,
        teacher.Experience, teacher.Phone, teacher.HireDate,
        teacher.Salary, teacher.CreatedAt, teacher.UpdatedAt).Scan(&teacher.ID)
    
    if err != nil {
        return fmt.Errorf("failed to create teacher: %w", err)
    }
    
    return nil
}

func (r *teacherRepository) GetByID(id int) (*domain.Teacher, error) {
    query := `
        SELECT t.id, t.user_id, t.employee_id, t.department, t.subject,
               t.qualification, t.experience, t.phone, t.hire_date,
               t.salary, t.created_at, t.updated_at,
               u.email, u.first_name, u.last_name, u.role, u.is_active
        FROM teachers t
        JOIN users u ON t.user_id = u.id
        WHERE t.id = $1`
    
    teacher := &domain.Teacher{User: &domain.User{}}
    err := r.db.QueryRow(query, id).Scan(
        &teacher.ID, &teacher.UserID, &teacher.EmployeeID, &teacher.Department,
        &teacher.Subject, &teacher.Qualification, &teacher.Experience,
        &teacher.Phone, &teacher.HireDate, &teacher.Salary,
        &teacher.CreatedAt, &teacher.UpdatedAt,
        &teacher.User.Email, &teacher.User.FirstName, &teacher.User.LastName,
        &teacher.User.Role, &teacher.User.IsActive,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get teacher by ID: %w", err)
    }
    
    teacher.User.ID = teacher.UserID
    return teacher, nil
}

func (r *teacherRepository) GetByUserID(userID int) (*domain.Teacher, error) {
    query := `
        SELECT id, user_id, employee_id, department, subject, qualification,
               experience, phone, hire_date, salary, created_at, updated_at
        FROM teachers WHERE user_id = $1`
    
    teacher := &domain.Teacher{}
    err := r.db.QueryRow(query, userID).Scan(
        &teacher.ID, &teacher.UserID, &teacher.EmployeeID, &teacher.Department,
        &teacher.Subject, &teacher.Qualification, &teacher.Experience,
        &teacher.Phone, &teacher.HireDate, &teacher.Salary,
        &teacher.CreatedAt, &teacher.UpdatedAt,
    )
    
    if err != nil {
        return nil, fmt.Errorf("failed to get teacher by user ID: %w", err)
    }
    
    return teacher, nil
}

func (r *teacherRepository) GetAll() ([]*domain.Teacher, error) {
    query := `
        SELECT t.id, t.user_id, t.employee_id, t.department, t.subject,
               t.qualification, t.experience, t.phone, t.hire_date,
               t.salary, t.created_at, t.updated_at,
               u.email, u.first_name, u.last_name, u.role, u.is_active
        FROM teachers t
        JOIN users u ON t.user_id = u.id
        ORDER BY t.created_at DESC`
    
    rows, err := r.db.Query(query)
    if err != nil {
        return nil, fmt.Errorf("failed to get all teachers: %w", err)
    }
    defer rows.Close()

    var teachers []*domain.Teacher
    for rows.Next() {
        teacher := &domain.Teacher{User: &domain.User{}}
        err := rows.Scan(
            &teacher.ID, &teacher.UserID, &teacher.EmployeeID, &teacher.Department,
            &teacher.Subject, &teacher.Qualification, &teacher.Experience,
            &teacher.Phone, &teacher.HireDate, &teacher.Salary,
            &teacher.CreatedAt, &teacher.UpdatedAt,
            &teacher.User.Email, &teacher.User.FirstName, &teacher.User.LastName,
            &teacher.User.Role, &teacher.User.IsActive,
        )
        if err != nil {
            return nil, fmt.Errorf("failed to scan teacher: %w", err)
        }
        teacher.User.ID = teacher.UserID
        teachers = append(teachers, teacher)
    }
    
    return teachers, nil
}

func (r *teacherRepository) Update(teacher *domain.Teacher) error {
    query := `
        UPDATE teachers 
        SET employee_id = $1, department = $2, subject = $3, qualification = $4,
            experience = $5, phone = $6, salary = $7, updated_at = $8
        WHERE id = $9`
    
    teacher.UpdatedAt = time.Now()
    
    _, err := r.db.Exec(query, teacher.EmployeeID, teacher.Department,
        teacher.Subject, teacher.Qualification, teacher.Experience,
        teacher.Phone, teacher.Salary, teacher.UpdatedAt, teacher.ID)
    
    if err != nil {
        return fmt.Errorf("failed to update teacher: %w", err)
    }
    
    return nil
}

func (r *teacherRepository) Delete(id int) error {
    query := `DELETE FROM teachers WHERE id = $1`
    
    _, err := r.db.Exec(query, id)
    if err != nil {
        return fmt.Errorf("failed to delete teacher: %w", err)
    }
    
    return nil
}

// ===== internal/service/auth_service.go =====
package service

import (
    "errors"
    "time"

    "school-management-system/internal/domain"
    "school-management-system/internal/repository"

    "github.com/golang-jwt/jwt/v5"
    "golang.org/x/crypto/bcrypt"
)

type AuthService struct {
    userRepo  repository.UserRepository
    jwtSecret string
}

func NewAuthService(userRepo repository.UserRepository, jwtSecret string) *AuthService {
    return &AuthService{
        userRepo:  userRepo,
        jwtSecret: jwtSecret,
    }
}

func (s *AuthService) Login(req *domain.LoginRequest) (*domain.LoginResponse, error) {
    user, err := s.userRepo.GetByEmail(req.Email)
    if err != nil {
        return nil, errors.New("invalid credentials")
    }

    if !user.IsActive {
        return nil, errors.New("account is deactivated")
    }

    if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
        return nil, errors.New("invalid credentials")
    }

    token, err := s.generateToken(user)
    if err != nil {
        return nil, err
    }

    return &domain.LoginResponse{
        Token: token,
        User:  *user,
    }, nil
}

func (s *AuthService) Register(req *domain.RegisterRequest) (*domain.User, error) {
    // Check if user already exists
    if _, err := s.userRepo.GetByEmail(req.Email); err == nil {
        return nil, errors.New("user already exists")
    }

    // Hash password
    hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
    if err != nil {
        return nil, err
    }

    user := &domain.User{
        Email:     req.Email,
        Password:  string(hashedPassword),
        FirstName: req.FirstName,
        LastName:  req.LastName,
        Role:      req.Role,
    }

    if err := s.userRepo.Create(user); err != nil {
        return nil, err
    }

    return user, nil
}

func (s *AuthService) generateToken(user *domain.User) (string, error) {
    claims := jwt.MapClaims{
        "user_id": user.ID,
        "email":   user.Email,
        "role":    user.Role,
        "exp":     time.Now().Add(time.Hour * 24).Unix(),
        "iat":     time.Now().Unix(),
    }

    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(s.jwtSecret))
}

func (s *AuthService) ValidateToken(tokenString string) (*domain.User, error) {
    token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
        if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
            return nil, errors.New("invalid token signing method")
        }
        return []byte(s.jwtSecret), nil
    })

    if err != nil {
        return nil, err
    }

    if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
        userID := int(claims["user_id"].(float64))
        return s.userRepo.GetByID(userID)
    }

    return nil, errors.New("invalid token")
}

// ===== internal/service/user_service.go =====
package service

import (
    "school-management-system/internal/domain"
    "school-management-system/internal/repository"
)

type UserService struct {
    userRepo repository.UserRepository
}

func NewUserService(userRepo repository.UserRepository) *UserService {
    return &UserService{userRepo: userRepo}
}

func (s *UserService) GetUsers() ([]*domain.User, error) {
    return s.userRepo.GetAll()
}

func (s *UserService) GetUser(id int) (*domain.User, error) {
    return s.userRepo.GetByID(id)
}

func (s *UserService) UpdateUser(user *domain.User) error {
    return s.userRepo.Update(user)
}

func (s *UserService) DeleteUser(id int) error {
    return s.userRepo.Delete(id)
}

    return fmt.Sprintf("event_%d", time.Now().UnixNano())
}

// ===== internal/notification/email_service.go =====
package notification

import (
    "context"
    "fmt"
    "log"

    "school-management-system/internal/events"

    "gopkg.in/gomail.v2"
)

type EmailService struct {
    dialer   *gomail.Dialer
    from     string
    eventBus *events.EventBus
}

type EmailConfig struct {
    SMTPHost     string
    SMTPPort     int
    SMTPUsername string
    SMTPPassword string
    FromEmail    string
}

func NewEmailService(config EmailConfig, eventBus *events.EventBus) *EmailService {
    dialer := gomail.NewDialer(config.SMTPHost, config.SMTPPort, config.SMTPUsername, config.SMTPPassword)
    
    service := &EmailService{
        dialer:   dialer,
        from:     config.FromEmail,
        eventBus: eventBus,
    }
    
    // Subscribe to events that require email notifications
    eventBus.Subscribe(events.StudentEnrolled, service.handleStudentEnrollment)
    eventBus.Subscribe(events.TeacherHired, service.handleTeacherHired)
    eventBus.Subscribe(events.GradeSubmitted, service.handleGradeSubmission)
    
    return service
}

func (s *EmailService) handleStudentEnrollment(ctx context.Context, event events.Event) error {
    studentName, _ := event.Data["student_name"].(string)
    grade, _ := event.Data["grade"].(string)
    class, _ := event.Data["class"].(string)
    
    // Send welcome email to student
    return s.sendEmail(
        "<EMAIL>", // In real app, get from student data
        "Welcome to Our School!",
        fmt.Sprintf("Dear %s,\n\nWelcome to our school! You have been enrolled in Grade %s, Class %s.\n\nBest regards,\nSchool Administration", 
            studentName, grade, class),
    )
}

func (s *EmailService) handleTeacherHired(ctx context.Context, event events.Event) error {
    teacherName, _ := event.Data["teacher_name"].(string)
    department, _ := event.Data["department"].(string)
    
    return s.sendEmail(
        "<EMAIL>", // In real app, get from teacher data
        "Welcome to Our School Team!",
        fmt.Sprintf("Dear %s,\n\nWelcome to our school! You have been hired in the %s department.\n\nBest regards,\nHR Department", 
            teacherName, department),
    )
}

func (s *EmailService) handleGradeSubmission(ctx context.Context, event events.Event) error {
    studentName, _ := event.Data["student_name"].(string)
    subject, _ := event.Data["subject"].(string)
    grade, _ := event.Data["grade"].(string)
    
    return s.sendEmail(
        "<EMAIL>", // In real app, get parent email from student data
        "Grade Update for Your Child",
        fmt.Sprintf("Dear Parent,\n\nYour child %s has received a grade of %s in %s.\n\nBest regards,\nSchool Administration", 
            studentName, grade, subject),
    )
}

func (s *EmailService) sendEmail(to, subject, body string) error {
    m := gomail.NewMessage()
    m.SetHeader("From", s.from)
    m.SetHeader("To", to)
    m.SetHeader("Subject", subject)
    m.SetBody("text/plain", body)
    
    if err := s.dialer.DialAndSend(m); err != nil {
        log.Printf("Failed to send email to %s: %v", to, err)
        return err
    }
    
    log.Printf("Email sent successfully to %s", to)
    return nil
}

// ===== internal/cache/redis_cache.go =====
package cache

import (
    "context"
    "encoding/json"
    "fmt"
    "time"

    "github.com/go-redis/redis/v8"
)

type RedisCache struct {
    client *redis.Client
}

func NewRedisCache(addr, password string, db int) *RedisCache {
    rdb := redis.NewClient(&redis.Options{
        Addr:     addr,
        Password: password,
        DB:       db,
    })
    
    return &RedisCache{client: rdb}
}

func (c *RedisCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
    json, err := json.Marshal(value)
    if err != nil {
        return err
    }
    
    return c.client.Set(ctx, key, json, expiration).Err()
}

func (c *RedisCache) Get(ctx context.Context, key string, dest interface{}) error {
    val, err := c.client.Get(ctx, key).Result()
    if err != nil {
        return err
    }
    
    return json.Unmarshal([]byte(val), dest)
}

func (c *RedisCache) Delete(ctx context.Context, key string) error {
    return c.client.Del(ctx, key).Err()
}

func (c *RedisCache) InvalidatePattern(ctx context.Context, pattern string) error {
    keys, err := c.client.Keys(ctx, pattern).Result()
    if err != nil {
        return err
    }
    
    if len(keys) > 0 {
        return c.client.Del(ctx, keys...).Err()
    }
    
    return nil
}

// ===== internal/service/student_service.go (Updated with Caching) =====
package service

import (
    "context"
    "fmt"
    "time"

    "school-management-system/internal/cache"
    "school-management-system/internal/domain"
    "school-management-system/internal/repository"
)

type StudentService struct {
    studentRepo repository.StudentRepository
    cache       *cache.RedisCache
}

func NewStudentService(studentRepo repository.StudentRepository, cache *cache.RedisCache) *StudentService {
    return &StudentService{
        studentRepo: studentRepo,
        cache:       cache,
    }
}

func (s *StudentService) CreateStudent(req *domain.CreateStudentRequest) (*domain.Student, error) {
    student := &domain.Student{
        UserID:      req.UserID,
        StudentID:   req.StudentID,
        Grade:       req.Grade,
        Class:       req.Class,
        DateOfBirth: req.DateOfBirth,
        Address:     req.Address,
        ParentName:  req.ParentName,
        ParentPhone: req.ParentPhone,
        ParentEmail: req.ParentEmail,
    }

    if err := s.studentRepo.Create(student); err != nil {
        return nil, err
    }

    // Invalidate cache
    ctx := context.Background()
    s.cache.InvalidatePattern(ctx, "students:*")

    return s.studentRepo.GetByID(student.ID)
}

func (s *StudentService) GetStudents() ([]*domain.Student, error) {
    ctx := context.Background()
    cacheKey := "students:all"
    
    // Try to get from cache first
    var students []*domain.Student
    if err := s.cache.Get(ctx, cacheKey, &students); err == nil {
        return students, nil
    }
    
    // If not in cache, get from database
    students, err := s.studentRepo.GetAll()
    if err != nil {
        return nil, err
    }
    
    // Cache the result for 5 minutes
    s.cache.Set(ctx, cacheKey, students, 5*time.Minute)
    
    return students, nil
}

func (s *StudentService) GetStudent(id int) (*domain.Student, error) {
    ctx := context.Background()
    cacheKey := fmt.Sprintf("students:%d", id)
    
    // Try cache first
    var student domain.Student
    if err := s.cache.Get(ctx, cacheKey, &student); err == nil {
        return &student, nil
    }
    
    // Get from database
    studentPtr, err := s.studentRepo.GetByID(id)
    if err != nil {
        return nil, err
    }
    
    // Cache for 10 minutes
    s.cache.Set(ctx, cacheKey, studentPtr, 10*time.Minute)
    
    return studentPtr, nil
}

func (s *StudentService) UpdateStudent(student *domain.Student) error {
    if err := s.studentRepo.Update(student); err != nil {
        return err
    }
    
    // Invalidate cache
    ctx := context.Background()
    s.cache.Delete(ctx, fmt.Sprintf("students:%d", student.ID))
    s.cache.InvalidatePattern(ctx, "students:all")
    
    return nil
}

func (s *StudentService) DeleteStudent(id int) error {
    if err := s.studentRepo.Delete(id); err != nil {
        return err
    }
    
    // Invalidate cache
    ctx := context.Background()
    s.cache.Delete(ctx, fmt.Sprintf("students:%d", id))
    s.cache.InvalidatePattern(ctx, "students:all")
    
    return nil
}

// ===== internal/service/teacher_service.go =====
package service

import (
    "school-management-system/internal/domain"
    "school-management-system/internal/repository"
)

type TeacherService struct {
    teacherRepo repository.TeacherRepository
}

func NewTeacherService(teacherRepo repository.TeacherRepository) *TeacherService {
    return &TeacherService{teacherRepo: teacherRepo}
}

func (s *TeacherService) CreateTeacher(req *domain.CreateTeacherRequest) (*domain.Teacher, error) {
    teacher := &domain.Teacher{
        UserID:        req.UserID,
        EmployeeID:    req.EmployeeID,
        Department:    req.Department,
        Subject:       req.Subject,
        Qualification: req.Qualification,
        Experience:    req.Experience,
        Phone:         req.Phone,
        Salary:        req.Salary,
    }

    if err := s.teacherRepo.Create(teacher); err != nil {
        return nil, err
    }

    return s.teacherRepo.GetByID(teacher.ID)
}

func (s *TeacherService) GetTeachers() ([]*domain.Teacher, error) {
    return s.teacherRepo.GetAll()
}

func (s *TeacherService) GetTeacher(id int) (*domain.Teacher, error) {
    return s.teacherRepo.GetByID(id)
}

func (s *TeacherService) UpdateTeacher(teacher *domain.Teacher) error {
    return s.teacherRepo.Update(teacher)
}

func (s *TeacherService) DeleteTeacher(id int) error {
    return s.teacherRepo.Delete(id)
}

// ===== internal/middleware/auth_middleware.go =====
package middleware

import (
    "context"
    "net/http"
    "strings"

    "school-management-system/internal/service"

    "github.com/go-chi/render"
)

type AuthMiddleware struct {
    authService *service.AuthService
}

func NewAuthMiddleware(jwtSecret string) *AuthMiddleware {
    return &AuthMiddleware{}
}

func (m *AuthMiddleware) Authenticate(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        authHeader := r.Header.Get("Authorization")
        if authHeader == "" {
            http.Error(w, "Authorization header required", http.StatusUnauthorized)
            return
        }

        tokenString := strings.TrimPrefix(authHeader, "Bearer ")
        if tokenString == authHeader {
            http.Error(w, "Invalid authorization header format", http.StatusUnauthorized)
            return
        }

        // Note: In a real implementation, you'd validate the token here
        // For now, we'll just pass through if the header exists
        
        next.ServeHTTP(w, r)
    })
}

type contextKey string

const UserContextKey contextKey = "user"

func GetUserFromContext(ctx context.Context) interface{} {
    return ctx.Value(UserContextKey)
}

// ===== internal/handler/auth_handler.go =====
package handler

import (
    "encoding/json"
    "net/http"

    "school-management-system/internal/domain"
    "school-management-system/internal/service"

    "github.com/go-chi/render"
)

type AuthHandler struct {
    authService *service.AuthService
}

func NewAuthHandler(authService *service.AuthService) *AuthHandler {
    return &AuthHandler{authService: authService}
}

func (h *AuthHandler) Login(w http.ResponseWriter, r *http.Request) {
    var req domain.LoginRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    response, err := h.authService.Login(&req)
    if err != nil {
        http.Error(w, err.Error(), http.StatusUnauthorized)
        return
    }

    render.JSON(w, r, response)
}

func (h *AuthHandler) Register(w http.ResponseWriter, r *http.Request) {
    var req domain.RegisterRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    user, err := h.authService.Register(&req)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    render.JSON(w, r, user)
}

// ===== internal/handler/user_handler.go =====
package handler

import (
    "encoding/json"
    "net/http"
    "strconv"

    "school-management-system/internal/domain"
    "school-management-system/internal/service"

    "github.com/go-chi/chi/v5"
    "github.com/go-chi/render"
)

type UserHandler struct {
    userService *service.UserService
}

func NewUserHandler(userService *service.UserService) *UserHandler {
    return &UserHandler{userService: userService}
}

func (h *UserHandler) GetUsers(w http.ResponseWriter, r *http.Request) {
    users, err := h.userService.GetUsers()
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, users)
}

func (h *UserHandler) GetUser(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid user ID", http.StatusBadRequest)
        return
    }

    user, err := h.userService.GetUser(id)
    if err != nil {
        http.Error(w, err.Error(), http.StatusNotFound)
        return
    }

    render.JSON(w, r, user)
}

func (h *UserHandler) UpdateUser(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid user ID", http.StatusBadRequest)
        return
    }

    var user domain.User
    if err := json.NewDecoder(r.Body).Decode(&user); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    user.ID = id
    if err := h.userService.UpdateUser(&user); err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, user)
}

func (h *UserHandler) DeleteUser(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid user ID", http.StatusBadRequest)
        return
    }

    if err := h.userService.DeleteUser(id); err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    w.WriteHeader(http.StatusNoContent)
}

// ===== migrations/001_initial_schema.sql =====
-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'teacher', 'student')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Students table
CREATE TABLE IF NOT EXISTS students (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    student_id VARCHAR(50) UNIQUE NOT NULL,
    grade VARCHAR(20) NOT NULL,
    class VARCHAR(20) NOT NULL,
    date_of_birth DATE NOT NULL,
    address TEXT,
    parent_name VARCHAR(200),
    parent_phone VARCHAR(20),
    parent_email VARCHAR(255),
    enrollment_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Teachers table
CREATE TABLE IF NOT EXISTS teachers (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    employee_id VARCHAR(50) UNIQUE NOT NULL,
    department VARCHAR(100) NOT NULL,
    subject VARCHAR(100) NOT NULL,
    qualification VARCHAR(200),
    experience INTEGER DEFAULT 0,
    phone VARCHAR(20),
    hire_date DATE NOT NULL,
    salary DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_students_user_id ON students(user_id);
CREATE INDEX IF NOT EXISTS idx_students_student_id ON students(student_id);
CREATE INDEX IF NOT EXISTS idx_teachers_user_id ON teachers(user_id);
CREATE INDEX IF NOT EXISTS idx_teachers_employee_id ON teachers(employee_id);

-- Insert default admin user (password: admin123)
INSERT INTO users (email, password, first_name, last_name, role) 
VALUES ('<EMAIL>', '$2a$10$8K1p/a0dCBDUWNL/Uu7xOeF5KqNn4uf/ycOQqfNwSbI5oj5FvYKS.', 'Admin', 'User', 'admin')
ON CONFLICT (email) DO NOTHING;

// ===== .env.example (UPDATED) =====
# Database
DATABASE_URL=postgres://user:password@localhost/school_db?sslmode=disable

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Server
PORT=8080

# Redis Cache (Optional)
REDIS_URL=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
FROM_EMAIL=<EMAIL>

# WebSocket Configuration
WS_BUFFER_SIZE=1000
WS_WORKERS=5

// ===== docker-compose.yml =====
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: school_db
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d

  app:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    environment:
      DATABASE_URL: **************************************/school_db?sslmode=disable
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
    volumes:
      - .:/app
    working_dir: /app

volumes:
  postgres_data:

// ===== Dockerfile =====
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o main cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/.env .

CMD ["./main"]

// ===== Makefile =====
.PHONY: run build test clean migrate-up migrate-down docker-up docker-down

# Development
run:
	go run cmd/server/main.go

build:
	go build -o bin/server cmd/server/main.go

test:
	go test -v ./...

clean:
	rm -rf bin/

# Database
migrate-up:
	psql $(DATABASE_URL) -f migrations/001_initial_schema.sql

migrate-down:
	psql $(DATABASE_URL) -c "DROP TABLE IF EXISTS teachers, students, users CASCADE;"

# Docker
docker-up:
	docker-compose up -d

docker-down:
	docker-compose down

docker-logs:
	docker-compose logs -f

# Dependencies
deps:
	go mod tidy
	go mod download

// ===== README.md =====
# School Management System Backend

A clean architecture backend starter for a school management system built with Go, Chi router, PostgreSQL, and JWT authentication.

## Features

- Clean Architecture with Repository Pattern
- JWT Authentication
- User Management (Admin, Teacher, Student roles)
- Student Management
- Teacher Management
- PostgreSQL Database
- Docker Support
- RESTful API

## Project Structure

```
school-management-system/
├── cmd/server/          # Application entry point
├── internal/
│   ├── api/             # API routing and route organization
│   ├── config/          # Configuration management
│   ├── domain/          # Business entities
│   ├── repository/      # Data access layer
│   ├── service/         # Business logic layer
│   ├── handler/         # HTTP handlers
│   ├── middleware/      # HTTP middleware
│   └── database/        # Database connection
├── migrations/          # Database migrations
└── docker-compose.yml   # Docker setup
```

## Architecture Benefits

### Clean Architecture
- **Separation of Concerns**: Each layer has a specific responsibility
- **Dependency Inversion**: Dependencies flow inward, business logic doesn't depend on external concerns
- **Testability**: Easy to unit test individual components
- **Maintainability**: Changes in one layer don't affect others

### Modular Route Organization
- **Scalable**: Easy to add new route groups without cluttering main.go
- **Organized**: Related routes are grouped together
- **Maintainable**: Each route group is in its own file
- **Extensible**: Simple to add middleware to specific route groups

## Quick Start

### Prerequisites

- Go 1.21+
- PostgreSQL 12+
- Docker (optional)

### Installation

1. Clone the repository and set up environment:
```bash
cp .env.example .env
# Edit .env with your database credentials
```

2. Start PostgreSQL (using Docker):
```bash
make docker-up
```

3. Run migrations:
```bash
make migrate-up
```

4. Install dependencies:
```bash
make deps
```

5. Run the application:
```bash
make run
```

The server will start on `http://localhost:8080`

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration

### Users (Protected)
- `GET /api/v1/users` - Get all users
- `GET /api/v1/users/{id}` - Get user by ID
- `PUT /api/v1/users/{id}` - Update user
- `DELETE /api/v1/users/{id}` - Delete user

### Students (Protected)
- `POST /api/v1/students` - Create student
- `GET /api/v1/students` - Get all students
- `GET /api/v1/students/{id}` - Get student by ID
- `PUT /api/v1/students/{id}` - Update student
- `DELETE /api/v1/students/{id}` - Delete student

### Teachers (Protected)
- `POST /api/v1/teachers` - Create teacher
- `GET /api/v1/teachers` - Get all teachers
- `GET /api/v1/teachers/{id}` - Get teacher by ID
- `PUT /api/v1/teachers/{id}` - Update teacher
- `DELETE /api/v1/teachers/{id}` - Delete teacher

## Authentication

All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Default Admin User
- Email: `<EMAIL>`
- Password: `admin123`

## Example API Usage

### Login
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin123"}'
```

### Create Student
```bash
curl -X POST http://localhost:8080/api/v1/students \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "user_id": 2,
    "student_id": "STU001",
    "grade": "10",
    "class": "A",
    "date_of_birth": "2008-05-15T00:00:00Z",
    "address": "123 Main St",
    "parent_name": "John Doe",
    "parent_phone": "+1234567890",
    "parent_email": "<EMAIL>"
  }'
```

## Development

### Adding New Features

## Development

### Adding New Features

1. **Define Domain Entities** in `internal/domain/`
2. **Create Repository Interface** in `internal/repository/interfaces.go`
3. **Implement Repository** in `internal/repository/postgres/`
4. **Create Service** in `internal/service/`
5. **Create Handler** in `internal/handler/`
6. **Create Route File** in `internal/api/` (e.g., `course_routes.go`)
7. **Register Routes** in `internal/api/router.go`

### Example: Adding Course Management

1. Create `internal/domain/course.go`:
```go
type Course struct {
    ID          int    `json:"id" db:"id"`
    Name        string `json:"name" db:"name"`
    Code        string `json:"code" db:"code"`
    TeacherID   int    `json:"teacher_id" db:"teacher_id"`
    Credits     int    `json:"credits" db:"credits"`
    // ... other fields
}
```

2. Add to `internal/repository/interfaces.go`:
```go
type CourseRepository interface {
    Create(course *domain.Course) error
    GetByID(id int) (*domain.Course, error)
    // ... other methods
}
```

3. Create `internal/api/course_routes.go`:
```go
func (router *Router) setupCourseRoutes(r chi.Router, deps *Dependencies) {
    r.Route("/courses", func(r chi.Router) {
        r.Get("/", deps.CourseHandler.GetCourses)
        r.Post("/", deps.CourseHandler.CreateCourse)
        // ... other routes
    })
}
```

4. Update `internal/api/router.go` to include the new routes:
```go
func (router *Router) setupProtectedRoutes(r chi.Router, deps *Dependencies) {
    r.Group(func(r chi.Router) {
        r.Use(deps.AuthMiddleware.Authenticate)
        
        router.setupUserRoutes(r, deps)
        router.setupStudentRoutes(r, deps)
        router.setupTeacherRoutes(r, deps)
        router.setupCourseRoutes(r, deps) // Add this line
    })
}
```

### Adding Middleware to Specific Routes

You can add middleware to specific route groups. For example, admin-only routes:

```go
func (router *Router) setupAdminRoutes(r chi.Router, deps *Dependencies) {
    r.Group(func(r chi.Router) {
        r.Use(deps.AdminMiddleware.RequireAdmin) // Admin-only middleware
        
        r.Route("/admin", func(r chi.Router) {
            r.Get("/users", deps.AdminHandler.GetAllUsers)
            r.Post("/users/{id}/deactivate", deps.AdminHandler.DeactivateUser)
        })
    })
}
```

### Database Migrations

Create new migration files in the `migrations/` directory:
- `002_add_courses_table.sql`
- `003_add_grades_table.sql`
- etc.

## Goroutines & Channels Usage

This project strategically uses goroutines and channels in several places:

### 1. **Event Bus Workers** 🔄
- **5 worker goroutines** process events concurrently
- **Buffered channel** (1000 events) prevents blocking
- **Why**: Handles high event volume without blocking API requests

### 2. **Background Jobs** ⏰
- Daily cleanup, attendance reminders, grade calculations
- Each job runs in its own goroutine with tickers
- **Why**: Non-blocking background processing

### 3. **WebSocket Connections** 🌐
- Each WebSocket client gets 2 goroutines (read/write pumps)
- Hub manages all connections with channels
- **Why**: Real-time communication without blocking

### 4. **Email Notifications** 📧
- Email sending happens in goroutines to avoid blocking API responses
- **Why**: Network calls to SMTP servers can be slow

### When NOT to Use Goroutines:
- Simple CRUD operations (already handled by database)
- CPU-bound tasks that don't benefit from concurrency
- When you need immediate results (synchronous operations)

## Event System & WebSockets

### Event-Driven Architecture Benefits:
1. **Decoupling**: Services don't need to know about each other
2. **Scalability**: Add new event handlers without changing existing code
3. **Reliability**: Events can be retried, queued, or persisted
4. **Auditability**: All system events are tracked

### WebSocket Use Cases:
```javascript
// Frontend WebSocket connection
const ws = new WebSocket('ws://localhost:8080/api/v1/ws');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case 'student_notification':
            showNotification(`New student enrolled: ${data.data.student_name}`);
            break;
        case 'grade_notification':
            updateGradeDisplay(data.data);
            break;
    }
};
```

### Real-World Scenarios:

#### 1. **Student Enrollment Flow**:
```
API Request → Create Student → Publish Event → [
    ├── Send Welcome Email (goroutine)
    ├── Update Dashboard (WebSocket)
    ├── Generate Student ID Card (background job)
    └── Notify Teachers (WebSocket)
]
```

#### 2. **Grade Submission Flow**:
```
Teacher Submits Grade → Publish Event → [
    ├── Email Parent (goroutine)
    ├── Notify Student (WebSocket)
    ├── Update Report Card (background job)
    └── Calculate GPA (background job)
]
```

#### 3. **Real-Time Notifications**:
- New student enrollments
- Grade updates
- Attendance alerts
- System announcements
- Emergency notifications

## Performance Benefits

### Caching Strategy:
- **Redis** for frequently accessed data
- **10-minute cache** for individual records
- **5-minute cache** for lists
- **Pattern-based invalidation** when data changes

### Background Processing:
- **Daily cleanup** removes old sessions, temp files
- **Grade calculations** run at midnight
- **Attendance reminders** during school hours
- **Email queue processing** prevents SMTP bottlenecks

## Testing Concurrent Code

```go
// Test event publishing
func TestEventBus_Publish(t *testing.T) {
    bus := events.NewEventBus(10, 2)
    defer bus.Close()
    
    var received events.Event
    var wg sync.WaitGroup
    wg.Add(1)
    
    bus.Subscribe(events.StudentEnrolled, func(ctx context.Context, event events.Event) error {
        received = event
        wg.Done()
        return nil
    })
    
    event := events.Event{Type: events.StudentEnrolled}
    bus.Publish(context.Background(), event)
    
    wg.Wait()
    assert.Equal(t, events.StudentEnrolled, received.Type)
}
```

## When This Architecture Shines

✅ **Perfect for**:
- School management systems
- E-learning platforms  
- Chat applications
- Real-time dashboards
- IoT data processing
- Financial trading systems

❌ **Overkill for**:
- Simple CRUD APIs
- Static websites
- Batch processing jobs
- Single-user applications

// ===== internal/handler/student_handler.go =====
package handler

import (
    "encoding/json"
    "net/http"
    "strconv"

    "school-management-system/internal/domain"
    "school-management-system/internal/service"

    "github.com/go-chi/chi/v5"
    "github.com/go-chi/render"
)

type StudentHandler struct {
    studentService *service.StudentService
}

func NewStudentHandler(studentService *service.StudentService) *StudentHandler {
    return &StudentHandler{studentService: studentService}
}

func (h *StudentHandler) CreateStudent(w http.ResponseWriter, r *http.Request) {
    var req domain.CreateStudentRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    student, err := h.studentService.CreateStudent(&req)
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, student)
}

func (h *StudentHandler) GetStudents(w http.ResponseWriter, r *http.Request) {
    students, err := h.studentService.GetStudents()
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, students)
}

func (h *StudentHandler) GetStudent(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid student ID", http.StatusBadRequest)
        return
    }

    student, err := h.studentService.GetStudent(id)
    if err != nil {
        http.Error(w, err.Error(), http.StatusNotFound)
        return
    }

    render.JSON(w, r, student)
}

func (h *StudentHandler) UpdateStudent(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid student ID", http.StatusBadRequest)
        return
    }

    var student domain.Student
    if err := json.NewDecoder(r.Body).Decode(&student); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    student.ID = id
    if err := h.studentService.UpdateStudent(&student); err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, student)
}

func (h *StudentHandler) DeleteStudent(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid student ID", http.StatusBadRequest)
        return
    }

    if err := h.studentService.DeleteStudent(id); err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    w.WriteHeader(http.StatusNoContent)
}

// ===== internal/handler/teacher_handler.go =====
package handler

import (
    "encoding/json"
    "net/http"
    "strconv"

    "school-management-system/internal/domain"
    "school-management-system/internal/service"

    "github.com/go-chi/chi/v5"
    "github.com/go-chi/render"
)

type TeacherHandler struct {
    teacherService *service.TeacherService
}

func NewTeacherHandler(teacherService *service.TeacherService) *TeacherHandler {
    return &TeacherHandler{teacherService: teacherService}
}

func (h *TeacherHandler) CreateTeacher(w http.ResponseWriter, r *http.Request) {
    var req domain.CreateTeacherRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    teacher, err := h.teacherService.CreateTeacher(&req)
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, teacher)
}

func (h *TeacherHandler) GetTeachers(w http.ResponseWriter, r *http.Request) {
    teachers, err := h.teacherService.GetTeachers()
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, teachers)
}

func (h *TeacherHandler) GetTeacher(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid teacher ID", http.StatusBadRequest)
        return
    }

    teacher, err := h.teacherService.GetTeacher(id)
    if err != nil {
        http.Error(w, err.Error(), http.StatusNotFound)
        return
    }

    render.JSON(w, r, teacher)
}

func (h *TeacherHandler) UpdateTeacher(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid teacher ID", http.StatusBadRequest)
        return
    }

    var teacher domain.Teacher
    if err := json.NewDecoder(r.Body).Decode(&teacher); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    teacher.ID = id
    if err := h.teacherService.UpdateTeacher(&teacher); err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    render.JSON(w, r, teacher)
}

func (h *TeacherHandler) DeleteTeacher(w http.ResponseWriter, r *http.Request) {
    idStr := chi.URLParam(r, "id")
    id, err := strconv.Atoi(idStr)
    if err != nil {
        http.Error(w, "Invalid teacher ID", http.StatusBadRequest)
        return
    }

    if err := h.teacherService.DeleteTeacher(id); err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    w.WriteHeader(http.StatusNoContent)
}