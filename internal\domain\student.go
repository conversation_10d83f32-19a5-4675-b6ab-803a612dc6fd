package domain

import (
    "time"
)

type Student struct {
    ID           int       `json:"id" db:"id"`
    UserID       int       `json:"user_id" db:"user_id"`
    StudentID    string    `json:"student_id" db:"student_id"`
    Grade        string    `json:"grade" db:"grade"`
    Class        string    `json:"class" db:"class"`
    DateOfBirth  time.Time `json:"date_of_birth" db:"date_of_birth"`
    Address      string    `json:"address" db:"address"`
    ParentName   string    `json:"parent_name" db:"parent_name"`
    ParentPhone  string    `json:"parent_phone" db:"parent_phone"`
    ParentEmail  string    `json:"parent_email" db:"parent_email"`
    EnrollmentDate time.Time `json:"enrollment_date" db:"enrollment_date"`
    CreatedAt    time.Time `json:"created_at" db:"created_at"`
    UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
    
    // Embedded user information
    User *User `json:"user,omitempty"`
}

type CreateStudentRequest struct {
    UserID       int       `json:"user_id"`
    StudentID    string    `json:"student_id"`
    Grade        string    `json:"grade"`
    Class        string    `json:"class"`
    DateOfBirth  time.Time `json:"date_of_birth"`
    Address      string    `json:"address"`
    ParentName   string    `json:"parent_name"`
    ParentPhone  string    `json:"parent_phone"`
    ParentEmail  string    `json:"parent_email"`
}
